"""
기술적 지표 계산 모듈
조건부 진입을 위한 RSI, Stochastic RSI 계산
"""

import numpy as np
from typing import List, Dict, Tuple, Optional


def calculate_rsi(prices: List[float], period: int = 14) -> List[float]:
    """
    RSI (Relative Strength Index) 계산 - 웹사이트 방식 (EMA 사용)

    Args:
        prices: 종가 리스트
        period: RSI 계산 기간 (기본값: 14)

    Returns:
        RSI 값 리스트
    """
    if len(prices) < period + 1:
        return [50.0] * len(prices)  # 데이터 부족 시 중립값 반환

    prices = np.array(prices)
    deltas = np.diff(prices)

    # 상승분과 하락분 분리
    up = np.where(deltas > 0, deltas, 0)
    down = np.where(deltas < 0, -deltas, 0)

    rsi_values = []

    # 첫 번째 값은 중립값으로 설정
    rsi_values.append(50.0)

    # EMA 계산을 위한 alpha 값 (웹사이트 방식: com=period-1)
    # alpha = 1 / (1 + com) = 1 / (1 + 13) = 1/14
    alpha = 1.0 / period

    # 첫 번째 평균 (SMA로 시작)
    if len(up) >= period:
        avg_gain = np.mean(up[:period])
        avg_loss = np.mean(down[:period])

        # 첫 번째 RSI 계산
        if avg_loss == 0:
            rsi = 100.0 if avg_gain > 0 else 50.0
        elif avg_gain == 0:
            rsi = 0.0
        else:
            rs = avg_gain / avg_loss
            rsi = 100.0 - (100.0 / (1.0 + rs))

        # period 개수만큼 중립값으로 채우고 마지막에 첫 번째 RSI 값
        for _ in range(period - 1):
            rsi_values.append(50.0)
        rsi_values.append(rsi)

        # 이후 값들은 EMA 방식으로 계산
        for i in range(period, len(up)):
            # EMA 업데이트 (웹사이트 방식)
            avg_gain = alpha * up[i] + (1 - alpha) * avg_gain
            avg_loss = alpha * down[i] + (1 - alpha) * avg_loss

            if avg_loss == 0:
                rsi = 100.0 if avg_gain > 0 else 50.0
            elif avg_gain == 0:
                rsi = 0.0
            else:
                rs = avg_gain / avg_loss
                rsi = 100.0 - (100.0 / (1.0 + rs))

            rsi_values.append(rsi)

    # 길이 맞추기
    while len(rsi_values) < len(prices):
        rsi_values.append(50.0)

    return rsi_values


def calculate_stochastic(highs: List[float], lows: List[float], closes: List[float], 
                        k_period: int = 14, d_period: int = 3) -> Tuple[List[float], List[float]]:
    """
    Stochastic Oscillator 계산
    
    Args:
        highs: 고가 리스트
        lows: 저가 리스트
        closes: 종가 리스트
        k_period: %K 계산 기간 (기본값: 14)
        d_period: %D 계산 기간 (기본값: 3)
    
    Returns:
        (%K 값 리스트, %D 값 리스트)
    """
    if len(closes) < k_period:
        return [50.0] * len(closes), [50.0] * len(closes)
    
    highs = np.array(highs)
    lows = np.array(lows)
    closes = np.array(closes)
    
    k_values = []
    
    # %K 계산
    for i in range(len(closes)):
        if i < k_period - 1:
            k_values.append(50.0)
        else:
            highest_high = np.max(highs[i - k_period + 1:i + 1])
            lowest_low = np.min(lows[i - k_period + 1:i + 1])
            
            if highest_high == lowest_low:
                k_value = 50.0
            else:
                k_value = ((closes[i] - lowest_low) / (highest_high - lowest_low)) * 100
            
            k_values.append(k_value)
    
    # %D 계산 (K의 이동평균)
    d_values = []
    for i in range(len(k_values)):
        if i < d_period - 1:
            d_values.append(50.0)
        else:
            d_value = np.mean(k_values[i - d_period + 1:i + 1])
            d_values.append(d_value)
    
    return k_values, d_values


def calculate_stochastic_rsi(prices: List[float], rsi_period: int = 14, 
                           stoch_period: int = 14, k_period: int = 3, d_period: int = 3) -> Tuple[List[float], List[float], List[float]]:
    """
    Stochastic RSI 계산
    
    Args:
        prices: 종가 리스트
        rsi_period: RSI 계산 기간 (기본값: 14)
        stoch_period: Stochastic 계산 기간 (기본값: 14)
        k_period: %K 스무딩 기간 (기본값: 3)
        d_period: %D 스무딩 기간 (기본값: 3)
    
    Returns:
        (RSI 값 리스트, Stoch RSI %K 리스트, Stoch RSI %D 리스트)
    """
    # 1. RSI 계산
    rsi_values = calculate_rsi(prices, rsi_period)
    
    if len(rsi_values) < stoch_period:
        return rsi_values, [50.0] * len(rsi_values), [50.0] * len(rsi_values)
    
    # 2. RSI에 Stochastic 적용
    stoch_rsi_raw = []
    
    for i in range(len(rsi_values)):
        if i < stoch_period - 1:
            stoch_rsi_raw.append(50.0)
        else:
            rsi_slice = rsi_values[i - stoch_period + 1:i + 1]
            highest_rsi = max(rsi_slice)
            lowest_rsi = min(rsi_slice)
            
            if highest_rsi == lowest_rsi:
                stoch_rsi = 50.0
            else:
                stoch_rsi = ((rsi_values[i] - lowest_rsi) / (highest_rsi - lowest_rsi)) * 100
            
            stoch_rsi_raw.append(stoch_rsi)
    
    # 3. %K 스무딩 (Stoch RSI의 이동평균)
    k_values = []
    for i in range(len(stoch_rsi_raw)):
        if i < k_period - 1:
            k_values.append(50.0)
        else:
            k_value = np.mean(stoch_rsi_raw[i - k_period + 1:i + 1])
            k_values.append(k_value)
    
    # 4. %D 스무딩 (K의 이동평균)
    d_values = []
    for i in range(len(k_values)):
        if i < d_period - 1:
            d_values.append(50.0)
        else:
            d_value = np.mean(k_values[i - d_period + 1:i + 1])
            d_values.append(d_value)
    
    return rsi_values, k_values, d_values


def analyze_conditional_entry(candles: List[Dict], 
                            rsi_long_threshold: float = 20.0,
                            rsi_short_threshold: float = 80.0) -> Dict:
    """
    조건부 진입 분석
    
    Args:
        candles: 캔들 데이터 리스트 [{"open": float, "high": float, "low": float, "close": float, "volume": float}]
        rsi_long_threshold: 롱 진입을 위한 RSI 최소값
        rsi_short_threshold: 숏 진입을 위한 RSI 최대값
    
    Returns:
        {
            "signal": "long" | "short" | "wait",
            "rsi": float,
            "stoch_rsi_k": float,
            "stoch_rsi_d": float,
            "current_price": float
        }
    """
    if len(candles) < 30:  # 최소 데이터 요구량
        return {
            "signal": "wait",
            "rsi": 50.0,
            "stoch_rsi_k": 50.0,
            "stoch_rsi_d": 50.0,
            "current_price": candles[-1]["close"] if candles else 0.0
        }
    
    # 가격 데이터 추출
    closes = [candle["close"] for candle in candles]
    
    # Stochastic RSI 계산
    rsi_values, stoch_k_values, stoch_d_values = calculate_stochastic_rsi(closes)
    
    # 최신 값들
    current_rsi = rsi_values[-1]
    current_stoch_k = stoch_k_values[-1]
    current_stoch_d = stoch_d_values[-1]
    current_price = closes[-1]
    
    # 진입 신호 판단
    signal = "wait"

    # 디버깅을 위한 상세 로그
    print(f"[DEBUG] {closes[-1]:.6f} - RSI: {current_rsi:.2f}, K: {current_stoch_k:.2f}, D: {current_stoch_d:.2f}")
    print(f"[DEBUG] K > D: {current_stoch_k > current_stoch_d} ({current_stoch_k:.2f} > {current_stoch_d:.2f})")
    print(f"[DEBUG] K < D: {current_stoch_k < current_stoch_d} ({current_stoch_k:.2f} < {current_stoch_d:.2f})")
    print(f"[DEBUG] RSI > {rsi_long_threshold}: {current_rsi > rsi_long_threshold}")
    print(f"[DEBUG] RSI < {rsi_short_threshold}: {current_rsi < rsi_short_threshold}")

    if current_stoch_k > current_stoch_d and current_rsi > rsi_long_threshold:
        signal = "long"
        print(f"[DEBUG] 롱 신호 생성")
    elif current_stoch_k < current_stoch_d and current_rsi < rsi_short_threshold:
        signal = "short"
        print(f"[DEBUG] 숏 신호 생성")
    else:
        print(f"[DEBUG] 대기 신호")
    
    return {
        "signal": signal,
        "rsi": current_rsi,
        "stoch_rsi_k": current_stoch_k,
        "stoch_rsi_d": current_stoch_d,
        "current_price": current_price
    }


def update_realtime_candle(candles: List[Dict], current_price: float, current_timestamp: int) -> List[Dict]:
    """
    실시간 가격으로 현재 캔들 업데이트
    
    Args:
        candles: 기존 캔들 데이터
        current_price: 현재 가격
        current_timestamp: 현재 타임스탬프 (밀리초)
    
    Returns:
        업데이트된 캔들 데이터
    """
    if not candles:
        return candles
    
    updated_candles = candles.copy()
    last_candle = updated_candles[-1].copy()
    
    # 4시간 = 4 * 60 * 60 * 1000 = 14400000 밀리초
    candle_interval_ms = 4 * 60 * 60 * 1000
    
    # 현재 캔들의 시작 시간 계산
    current_candle_start = (current_timestamp // candle_interval_ms) * candle_interval_ms
    
    # 마지막 캔들의 시작 시간
    last_candle_start = last_candle["timestamp"]
    
    if current_candle_start > last_candle_start:
        # 새로운 캔들 시작
        new_candle = {
            "timestamp": current_candle_start,
            "open": current_price,
            "high": current_price,
            "low": current_price,
            "close": current_price,
            "volume": 0.0
        }
        updated_candles.append(new_candle)
        
        # 오래된 캔들 제거 (최대 100개 유지)
        if len(updated_candles) > 100:
            updated_candles = updated_candles[-100:]
    else:
        # 현재 캔들 업데이트
        last_candle["close"] = current_price
        last_candle["high"] = max(last_candle["high"], current_price)
        last_candle["low"] = min(last_candle["low"], current_price)
        updated_candles[-1] = last_candle
    
    return updated_candles
