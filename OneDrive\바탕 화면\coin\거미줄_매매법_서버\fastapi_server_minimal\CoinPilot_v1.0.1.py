#CoinPilot.py
from binance.client import Client
import tkinter as tk
from tkinter import messagebox
import requests
import logging
import tkinter.simpledialog as sd
import threading
import time
from tkinter import messagebox, simpledialog as sd, ttk
import re
from tkinter import ttk
import pathlib
import platform
import os
import sys
import subprocess
import tempfile
import webbrowser

# 로그 비활성화 - 필요시에만 사용
# logging.basicConfig(filename="CoinPilot.log", level=logging.DEBUG)
CURRENT_VERSION = "1.0.1"
API_BASE_URL = "http://*************:8000"
API_HEADERS = {"x-api-key": "tjdwn123!@!@"}


class CustomTable(tk.Frame):
    """개별 셀 색상을 지원하는 커스텀 테이블"""

    def __init__(self, parent, headers, theme_colors, app_instance=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.headers = headers
        self.theme_colors = theme_colors
        self.app_instance = app_instance  # CoinPilotApp 인스턴스 직접 저장
        self.rows = []
        self.row_widgets = {}  # {row_index: [widget_list]} - 깜빡임 방지용
        self.row_data = {}     # {row_index: values} - 데이터 비교용
        self.cell_colors = {}  # {(row, col): color}
        self.cell_texts = {}   # {(row, col): text}
        self.row_height = 25
        self.header_height = 30
        self.col_widths = [12, 8, 12, 12, 12, 12, 12, 12, 8, 8]  # 기본 너비 비율

        # 스크롤바 설정
        self.canvas = tk.Canvas(self, bg=theme_colors["panel_bg"], highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.safe_yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=theme_colors["panel_bg"])

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.update_scroll_region()
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # 레이아웃 - 스크롤바 숨김
        self.canvas.pack(side="left", fill="both", expand=True)
        # self.scrollbar.pack(side="right", fill="y")  # 스크롤바 숨김

        # 헤더 생성
        self.create_headers()

        # 마우스 휠 바인딩
        self.bind_mousewheel()

        # 너비 자동 계산을 위한 바인딩
        self.bind("<Configure>", self.on_resize)

        # 위젯이 표시된 후 너비 계산
        self.bind("<Map>", lambda e: self.after_idle(self.update_column_widths))

    def update_scroll_region(self):
        """스크롤 영역을 내용에 맞게 업데이트"""
        try:
            # 스크롤 가능한 영역 계산
            self.canvas.update_idletasks()
            bbox = self.canvas.bbox("all")

            if bbox:
                # 실제 내용이 있는 경우에만 스크롤 영역 설정
                canvas_height = self.canvas.winfo_height()
                content_height = bbox[3] - bbox[1]

                # 내용이 캔버스보다 작으면 스크롤 비활성화
                if content_height <= canvas_height:
                    self.canvas.configure(scrollregion=(0, 0, 0, 0))
                else:
                    self.canvas.configure(scrollregion=bbox)
            else:
                # 내용이 없으면 스크롤 비활성화
                self.canvas.configure(scrollregion=(0, 0, 0, 0))
        except:
            pass

    def safe_yview(self, *args):
        """안전한 스크롤 - 범위 제한"""
        try:
            # 현재 스크롤 영역 확인
            scrollregion = self.canvas.cget("scrollregion")
            if not scrollregion or scrollregion == "0 0 0 0":
                return  # 스크롤할 내용이 없으면 무시

            # 일반적인 스크롤 실행
            self.canvas.yview(*args)
        except:
            pass

    def bind_mousewheel(self):
        """마우스 휠 스크롤 바인딩 - 스크롤 범위 제한"""
        def _on_mousewheel(event):
            # 현재 스크롤 위치 확인
            current_top, current_bottom = self.canvas.yview()

            # 스크롤 방향 계산
            scroll_direction = int(-1*(event.delta/120))

            # 위로 스크롤 시 (scroll_direction < 0) - 맨 위에서 더 위로 스크롤 방지
            if scroll_direction < 0 and current_top <= 0:
                return

            # 아래로 스크롤 시 (scroll_direction > 0) - 맨 아래에서 더 아래로 스크롤 방지
            if scroll_direction > 0 and current_bottom >= 1:
                return

            # 스크롤 실행
            self.canvas.yview_scroll(scroll_direction, "units")

        self.canvas.bind("<MouseWheel>", _on_mousewheel)
        self.scrollable_frame.bind("<MouseWheel>", _on_mousewheel)

    def on_resize(self, event):
        """테이블 크기 변경 시 컬럼 너비 재계산"""
        if event.widget == self:
            self.update_column_widths()

    def update_column_widths(self):
        """컬럼 너비를 테이블 너비에 맞게 자동 계산"""
        try:
            # 여러 번 업데이트하여 정확한 크기 확보
            for _ in range(3):
                self.update_idletasks()

            # 스크롤바가 숨겨져 있으므로 여백 없음
            total_width = self.winfo_width() - 10  # 패딩만 포함

            # 최소 너비 확보
            if total_width < 100:
                # 부모 위젯의 너비 사용
                parent_width = self.master.winfo_width() if self.master else 800
                total_width = max(parent_width - 20, 600)  # 최소 600px 보장

            if total_width > 100:  # 유효한 너비인 경우
                # 비율에 따른 실제 픽셀 너비 계산
                total_ratio = sum(self.col_widths)
                self.actual_widths = []
                remaining_width = total_width

                # 마지막 컬럼을 제외하고 너비 계산
                for i, ratio in enumerate(self.col_widths[:-1]):
                    width = int(total_width * ratio / total_ratio)
                    self.actual_widths.append(width)
                    remaining_width -= width

                # 마지막 컬럼은 남은 너비 모두 사용 (정확한 맞춤)
                self.actual_widths.append(max(remaining_width, 50))

                # 헤더와 모든 행의 너비 업데이트
                self.update_all_widths()
                return True
        except:
            pass
        return False

    def update_all_widths(self):
        """헤더와 모든 행의 너비 업데이트"""
        if not hasattr(self, 'actual_widths'):
            return

        # 헤더 너비 업데이트 - 픽셀 단위로 정확하게 설정
        header_frame = self.scrollable_frame.winfo_children()[0] if self.scrollable_frame.winfo_children() else None
        if header_frame:
            for i, child in enumerate(header_frame.winfo_children()):
                if i < len(self.actual_widths):
                    # 픽셀 단위로 정확한 너비 설정
                    child.config(width=max(1, self.actual_widths[i] // 7))  # 더 정확한 문자 단위 변환

        # 모든 행의 너비 업데이트
        for row_widgets in self.row_widgets.values():
            for i, widget in enumerate(row_widgets):
                if i < len(self.actual_widths) and hasattr(widget, 'config'):
                    if isinstance(widget, tk.Label):
                        # 픽셀 단위로 정확한 너비 설정
                        widget.config(width=max(1, self.actual_widths[i] // 7))  # 더 정확한 문자 단위 변환
                    elif isinstance(widget, tk.Frame) and i == 9:  # Close 버튼 컨테이너
                        widget.config(width=self.actual_widths[i])

    def create_headers(self):
        """헤더 생성"""
        header_frame = tk.Frame(self.scrollable_frame, bg=self.theme_colors["header_bg"])
        header_frame.pack(fill="x", pady=(0, 1))

        for i, header in enumerate(self.headers):
            label = tk.Label(header_frame, text=header, font=("Segoe UI", 9, "bold"),
                           bg=self.theme_colors["header_bg"], fg=self.theme_colors["text_white"],
                           width=self.col_widths[i], anchor="center", relief="flat")
            label.pack(side="left", padx=0, fill="x", expand=True if i == len(self.headers)-1 else False)

    def clear_rows(self):
        """모든 행 삭제"""
        for widget in self.scrollable_frame.winfo_children()[1:]:  # 헤더 제외
            widget.destroy()
        self.rows.clear()
        self.row_widgets.clear()
        self.row_data.clear()
        self.cell_colors.clear()
        self.cell_texts.clear()

        # 스크롤 영역 업데이트
        self.update_scroll_region()

    def update_rows(self, all_rows_data):
        """행 업데이트 - 깜빡임 방지를 위해 변경된 부분만 수정"""
        # 기존 행들과 비교하여 변경된 부분만 업데이트
        for row_index, (values, cell_colors) in enumerate(all_rows_data):
            symbol = values[0]  # 심볼을 키로 사용

            # 기존 데이터와 비교
            if row_index in self.row_data and self.row_data[row_index] == values:
                continue  # 변경사항 없으면 스킵

            # 새로운 행이거나 변경된 행 처리
            if row_index >= len(self.rows):
                self.add_row(values, cell_colors)
            else:
                self.update_row(row_index, values, cell_colors)

        # 남은 행들 제거
        while len(self.rows) > len(all_rows_data):
            self.remove_row(len(self.rows) - 1)

        # 스크롤 영역 업데이트
        self.update_scroll_region()

    def add_row(self, values, cell_colors=None):
        """행 추가"""
        row_index = len(self.rows)
        self.rows.append(values)
        self.row_data[row_index] = values

        # 행 프레임 생성
        row_frame = tk.Frame(self.scrollable_frame, bg=self.theme_colors["panel_bg"])
        row_frame.pack(fill="x", pady=1)

        row_widgets = []

        for col_index, value in enumerate(values):
            # 색상 결정
            fg_color = self.get_cell_color(col_index, value, cell_colors)

            # 특별한 처리가 필요한 컬럼들
            if col_index == 9 and value == "Close":  # Close 버튼
                # 버튼을 가운데 정렬하기 위한 컨테이너 프레임
                btn_container = tk.Frame(row_frame, bg=self.theme_colors["panel_bg"],
                                       width=self.col_widths[col_index] * 8)
                btn_container.pack(side="left", padx=0, fill="both", expand=True)
                btn_container.pack_propagate(False)  # 크기 고정

                btn = tk.Button(btn_container, text=value, font=("Segoe UI", 8, "bold"),
                              bg=self.theme_colors["danger"], fg=self.theme_colors["text_white"],
                              relief="flat", cursor="hand2", width=6, height=1,
                              command=lambda r=row_index: self.on_close_click(r))
                btn.pack(expand=True)  # 컨테이너 내에서 가운데 정렬
                row_widgets.append(btn_container)
            else:
                label = tk.Label(row_frame, text=value, font=("Segoe UI", 9, "bold"),
                               bg=self.theme_colors["panel_bg"], fg=fg_color,
                               width=self.col_widths[col_index], anchor="center")
                label.pack(side="left", padx=0, fill="x", expand=True if col_index == len(values)-2 else False)
                row_widgets.append(label)

                # 오류 컬럼 클릭 이벤트 - "!"로 시작하는 오류 표시에만 적용
                if col_index == 8 and value != "없음" and value.startswith("!"):
                    label.configure(cursor="hand2")
                    label.bind("<Button-1>", lambda e, r=row_index: self.on_error_click(r))

        self.row_widgets[row_index] = row_widgets

        # 너비 업데이트
        if hasattr(self, 'actual_widths'):
            self.update_all_widths()

    def update_row(self, row_index, values, cell_colors=None):
        """기존 행 업데이트 - 깜빡임 방지"""
        if row_index not in self.row_widgets:
            return

        self.rows[row_index] = values
        self.row_data[row_index] = values
        row_widgets = self.row_widgets[row_index]

        for col_index, (widget, value) in enumerate(zip(row_widgets, values)):
            if col_index >= len(values):
                break

            # 색상 결정
            fg_color = self.get_cell_color(col_index, value, cell_colors)

            # 위젯 업데이트
            if hasattr(widget, 'config'):
                if isinstance(widget, tk.Label):
                    widget.config(text=value, fg=fg_color)
                elif isinstance(widget, tk.Frame) and col_index == 9:  # Close 버튼 컨테이너
                    # 컨테이너 내의 버튼 찾아서 업데이트
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Button):
                            child.config(text=value)
                            break

    def remove_row(self, row_index):
        """행 제거"""
        if row_index in self.row_widgets:
            # 위젯들 제거
            for widget in self.row_widgets[row_index]:
                if widget.winfo_exists():
                    widget.master.destroy()

            del self.row_widgets[row_index]
            del self.row_data[row_index]
            self.rows.pop(row_index)

    def get_cell_color(self, col_index, value, cell_colors=None):
        """셀 색상 결정"""
        # 우선순위: 직접 지정된 색상 > 자동 색상 > 기본 색상
        if cell_colors and col_index in cell_colors:
            return cell_colors[col_index]

        # 포지션 컬럼 (인덱스 1)
        if col_index == 1:
            if value == "Long":
                return self.theme_colors["long"]
            elif value == "Short":
                return self.theme_colors["short"]
            elif value == "대기중":
                return self.theme_colors["text_light"]

        # 손익 컬럼 (인덱스 5) - PNL
        elif col_index == 5 and "USDT" in str(value):
            try:
                pnl_value = float(value.replace(" USDT", ""))
                if pnl_value > 0:
                    return self.theme_colors["profit"]
                elif pnl_value < 0:
                    return self.theme_colors["loss"]
            except:
                pass

        # 수익률 컬럼 (인덱스 6) - ROI
        elif col_index == 6 and "%" in str(value):
            try:
                roi_value = float(value.replace("%", "").replace("+", ""))
                if roi_value > 0:
                    return self.theme_colors["profit"]
                elif roi_value < 0:
                    return self.theme_colors["loss"]
            except:
                pass

        return self.theme_colors["text_white"]

    def on_close_click(self, row_index):
        """Close 버튼 클릭 이벤트"""
        try:
            symbol = self.rows[row_index][0]  # 첫 번째 컬럼이 심볼
            side = self.rows[row_index][1]    # 두 번째 컬럼이 포지션 방향

            # 대기중인 코인은 종료 불가
            if side == "대기중":
                messagebox.showinfo("알림", f"{symbol}은 아직 포지션이 없어 종료할 수 없습니다.")
                return

            # CoinPilotApp 인스턴스 사용
            if self.app_instance and hasattr(self.app_instance, 'close_single_position_by_symbol'):
                self.app_instance.close_single_position_by_symbol(symbol)
            else:
                messagebox.showerror("오류", "포지션 종료 기능을 찾을 수 없습니다.")

        except Exception as e:
            messagebox.showerror("오류", f"포지션 종료 중 오류 발생: {e}")

    def on_error_click(self, row_index):
        """오류 컬럼 클릭 이벤트"""
        try:
            symbol = self.rows[row_index][0]  # 첫 번째 컬럼이 심볼

            # CoinPilotApp 인스턴스 사용
            if self.app_instance and hasattr(self.app_instance, 'show_error_details'):
                self.app_instance.show_error_details(symbol)
            else:
                messagebox.showerror("오류", "오류 상세 정보 기능을 찾을 수 없습니다.")

        except Exception as e:
            messagebox.showerror("오류", f"오류 상세 정보 표시 중 오류 발생: {e}")

# 테마 색상 정의 - 통일된 트레이딩 플랫폼 스타일
THEME_COLORS = {
    "light": {
        "bg": "#f8f9fa",
        "panel_bg": "#ffffff",
        "sidebar_bg": "#2c2c2c",
        "header_bg": "#2c2c2c",
        "primary": "#ffc107",    # 노란색 - 최소한으로만 사용
        "secondary": "#6c757d",  # 회색 - 메인 UI 색상
        "success": "#28a745",    # 롱 - 연두색
        "danger": "#dc3545",     # 숏 - 빨간색
        "text": "#212529",
        "text_light": "#6c757d",
        "text_white": "#ffffff",
        "text_dark": "#000000",
        "border": "#dee2e6",
        "hover": "#f1f3f4",
        "profit": "#28a745",     # 수익 - 적당한 밝기의 초록색
        "loss": "#ff4757",       # 손실 - 더 밝은 빨간색
        "neutral": "#6c757d",
        "long": "#28a745",       # 롱 포지션 - 어두운 초록색
        "short": "#ff4757",      # 숏 포지션 - 더 밝은 빨간색
        "card_bg": "#ffffff",    # 카드 배경
        "input_bg": "#ffffff",   # 입력 필드 배경
        "info": "#6c757d",       # 정보 색상 (회색)
        "warning": "#ffc107",    # 경고 색상 (노란색)
        "accent": "#ffc107",     # 액센트 색상 (노란색)
        "button_bg": "#f8f9fa",  # 버튼 배경
        "button_hover": "#e9ecef",  # 버튼 호버
        "success_bright": "#28a745"  # 밝은 초록색
    },
    "dark": {
        "bg": "#0d1117",         # 깊은 다크 배경
        "panel_bg": "#161b22",   # 패널 배경
        "sidebar_bg": "#0d1117",
        "header_bg": "#21262d",
        "primary": "#ffc107",    # 노란색 - 최소한으로만 사용
        "secondary": "#8b949e",  # 회색 - 메인 UI 색상
        "success": "#238636",    # 롱 - 어두운 연두색
        "success_bright": "#00ff88",  # ✅ 밝은 초록색 추가 (전략 실행중)
        "danger": "#da3633",     # 숏 - 어두운 빨간색
        "text": "#f0f6fc",       # 밝은 텍스트
        "text_light": "#8b949e",
        "text_white": "#ffffff",
        "text_dark": "#000000",
        "border": "#30363d",
        "hover": "#21262d",
        "profit": "#28a745",     # 수익 - 적당한 밝기의 초록색
        "loss": "#ff4757",       # 손실 - 더 밝은 빨간색
        "neutral": "#8b949e",
        "long": "#238636",       # 롱 포지션 - 어두운 초록색 (GitHub 스타일)
        "short": "#ff4757",      # 숏 포지션 - 더 밝은 빨간색
        "card_bg": "#161b22",    # 카드 배경
        "input_bg": "#0d1117",   # 입력 필드 배경
        "info": "#8b949e",       # 정보 색상 (회색)
        "warning": "#ffc107",    # 경고 색상 (노란색)
        "accent": "#ffc107",     # 액센트 색상 (노란색)
        "button_bg": "#21262d",  # 버튼 배경
        "button_hover": "#30363d"  # 버튼 호버
    }
}

# 현재 테마 설정
CURRENT_THEME = "dark"

def check_new_version():
    try:
        res = requests.get(f"{API_BASE_URL}/version", headers=API_HEADERS, timeout=5)
        if res.status_code != 200:
            return False, None

        data = res.json()
        latest_version = data.get("version")
        if latest_version and latest_version != CURRENT_VERSION:
            return True, data
        return False, None
    except Exception as e:
        print(f"[업데이트 확인 오류] {e}")
        return False, None
    
def find_root_app_path():
    path = pathlib.Path(sys.argv[0])
    for parent in path.parents:
        if parent.suffix == ".app":
            return str(parent)
    return None

def perform_update(data):
    import tempfile
    import shutil
    import zipfile

    try:
        system = platform.system().lower()
        latest_version = data.get("version")
        is_windows = system == "windows"
        is_macos = system == "darwin"

        if is_windows:
            download_url = data.get("windows_url")
            ext = ".exe"
        elif is_macos:
            download_url = data.get("mac_url")
            ext = ".app.zip"
        else:
            messagebox.showerror("업데이트 실패", "이 운영체제는 지원되지 않습니다.")
            return

        if not download_url:
            messagebox.showerror("업데이트 실패", "OS에 맞는 업데이트 파일이 없습니다.")
            return

        file_name = f"CoinPilot_v{latest_version}{ext}"
        current_path = os.path.abspath(sys.argv[0])
        app_dir = os.path.dirname(current_path)  # 현재 exe 위치
        save_path = os.path.join(app_dir, file_name)

        # ▶ 다운로드
        with requests.get(download_url, stream=True) as r:
            r.raise_for_status()
            with open(save_path, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

        if is_windows:
            messagebox.showinfo("업데이트 진행 중", "이전 버전 종료 후 새 버전이 실행됩니다.\n.")
            bat_path = os.path.join(tempfile.gettempdir(), "run_update.bat")

            bat_code = f"""@echo off
            echo 업데이트 진행 중입니다. 잠시만 기다려주세요...
            :loop
            tasklist | findstr /I "{os.path.basename(current_path)}" > nul
            if not errorlevel 1 (
                timeout /t 1 > nul
                goto loop
            )
            echo - 이전 버전 삭제 중...
            cd /d "{app_dir}"
            del "{os.path.basename(current_path)}"
            echo - 새 버전 실행 중...
            start "" "{os.path.basename(save_path)}"
            timeout /t 2 > nul
            start /min "" cmd /c del "%~f0"
            exit
            """

            with open(bat_path, "w", encoding="cp949") as bat:
                bat.write(bat_code)

            subprocess.Popen(["cmd", "/c", "start", "", bat_path])
            
        elif is_macos:
            # macOS 로직 유지
            unzip_dir = os.path.join(tempfile.gettempdir(), f"CoinPilot_v{latest_version}_unzipped")
            with zipfile.ZipFile(save_path, 'r') as zip_ref:
                zip_ref.extractall(unzip_dir)

            app_path = None
            for root, dirs, _ in os.walk(unzip_dir):
                for d in dirs:
                    if d.endswith(".app"):
                        app_path = os.path.join(root, d)
                        break
                if app_path:
                    break

            if not app_path:
                messagebox.showerror("오류", ".app 파일을 압축 내에서 찾을 수 없습니다.")
                return

            binary_path = os.path.join(app_path, "Contents", "MacOS", "CoinPilot")
            os.chmod(binary_path, 0o755)

            old_app_path = find_root_app_path()
            subprocess.Popen(["open", app_path])

            if old_app_path:
                try:
                    shutil.rmtree(old_app_path)
                except Exception as e:
                    print(f"기존 앱 삭제 실패: {e}")

        sys.exit()

    except Exception as e:
        messagebox.showerror("업데이트 오류", str(e))

# LoginWindow 클래스 위쪽 또는 아래쪽에 위치
def verify_api_whitelist(api_key: str, secret_key: str) -> bool:
    try:
        res = requests.post(
            f"{API_BASE_URL}/verify_api_key",
            headers=API_HEADERS,
            json={"api_key": api_key, "secret_key": secret_key},
            timeout=10
        )
        if res.status_code == 409:
            return False, 409
        elif res.status_code == 403:
            return False, 403
        elif res.status_code == 200:
            data = res.json()
            if data.get("success") is True:
                return True, 200
            else:
                logging.error(f"API 키 인증 실패: {data}")
                return False, 200
        else:
            logging.error(f"API 키 인증 서버 오류: {res.status_code}")
        return res.status_code == 200 and res.json().get("success") is True
    except Exception as e:
        logging.exception("API 키 인증 서버 요청 실패")
        return False, 500

class LoginWindow:
    def __init__(self, root):
        self.root = root
        self.root.title(f"CoinPilot Pro {CURRENT_VERSION}")
        self.root.geometry("1200x700")
        self.root.resizable(False, False)

        # 현재 테마 설정
        self.theme = CURRENT_THEME
        self.theme_colors = THEME_COLORS[self.theme]

        # 창을 화면 중앙에 배치
        self.center_window()

        self.root.configure(bg=self.theme_colors["bg"])
        self.root.after(1000, self.check_update_prompt)

        # 상단 헤더 바
        self.create_header()

        # 메인 컨테이너
        main_container = tk.Frame(self.root, bg=self.theme_colors["bg"])
        main_container.pack(fill="both", expand=True, padx=0, pady=0)

        # 중앙 로그인 영역
        self.create_login_area(main_container)

        # 우측 공지사항 및 차트 영역
        self.create_info_area(main_container)

    def center_window(self):
        """창을 화면 중앙에 배치"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_header(self):
        """상단 헤더 바 생성"""
        header_frame = tk.Frame(self.root, bg=self.theme_colors["header_bg"], height=60)
        header_frame.pack(fill="x", side="top")
        header_frame.pack_propagate(False)

        # 로고 영역
        logo_frame = tk.Frame(header_frame, bg=self.theme_colors["header_bg"])
        logo_frame.pack(side="left", padx=20, pady=10)

        tk.Label(logo_frame, text="CoinPilot Pro", font=("Segoe UI", 18, "bold"),
                bg=self.theme_colors["header_bg"], fg=self.theme_colors["text_white"]).pack(side="left")
        tk.Label(logo_frame, text=f"v{CURRENT_VERSION}", font=("Segoe UI", 10),
                bg=self.theme_colors["header_bg"], fg=self.theme_colors["text_light"]).pack(side="left", padx=(10, 0))

        # 우측 컨트롤
        controls_frame = tk.Frame(header_frame, bg=self.theme_colors["header_bg"])
        controls_frame.pack(side="right", padx=20, pady=10)





    def create_login_area(self, parent):
        """간단한 로그인 영역 생성 - 바이낸스 색감 유지"""
        login_container = tk.Frame(parent, bg=self.theme_colors["bg"], width=300)
        login_container.pack(side="left", fill="y", padx=20, pady=20)
        login_container.pack_propagate(False)

        # 로그인 제목
        title_frame = tk.Frame(login_container, bg=self.theme_colors["bg"])
        title_frame.pack(fill="x", pady=(20, 30))

        # 로고 아이콘
        logo_label = tk.Label(title_frame, text="🔐", font=("Segoe UI", 16),
                             bg=self.theme_colors["bg"], fg="#F0B90B")
        logo_label.pack(side="left")

        title_label = tk.Label(title_frame, text="Login", font=("Segoe UI", 18, "bold"),
                              bg=self.theme_colors["bg"], fg=self.theme_colors["text"])
        title_label.pack(side="left", padx=(10, 0))

        # 아이디 입력
        id_frame = tk.Frame(login_container, bg=self.theme_colors["bg"])
        id_frame.pack(fill="x", pady=(0, 15))

        tk.Label(id_frame, text="아이디:", font=("Segoe UI", 11),
                bg=self.theme_colors["bg"], fg=self.theme_colors["text"]).pack(anchor="w")

        # 아이디 입력 필드를 둥글게 만들기 위한 컨테이너
        username_container = tk.Frame(id_frame, bg="#1E2329", relief="flat", bd=0)
        username_container.pack(fill="x", pady=(5, 0))

        self.username = tk.Entry(username_container, font=("Segoe UI", 12), relief="flat", borderwidth=0,
                                bg="#1E2329", fg="#FFFFFF", insertbackground="#FFFFFF",
                                highlightthickness=0)
        self.username.pack(fill="x", padx=12, pady=8)

        # 비밀번호 입력
        pwd_frame = tk.Frame(login_container, bg=self.theme_colors["bg"])
        pwd_frame.pack(fill="x", pady=(0, 20))

        tk.Label(pwd_frame, text="비밀번호:", font=("Segoe UI", 11),
                bg=self.theme_colors["bg"], fg=self.theme_colors["text"]).pack(anchor="w")

        # 비밀번호 입력 필드를 둥글게 만들기 위한 컨테이너
        password_container = tk.Frame(pwd_frame, bg="#1E2329", relief="flat", bd=0)
        password_container.pack(fill="x", pady=(5, 0))

        self.password = tk.Entry(password_container, font=("Segoe UI", 12), relief="flat", borderwidth=0,
                                bg="#1E2329", fg="#FFFFFF", insertbackground="#FFFFFF", show="•",
                                highlightthickness=0)
        self.password.pack(fill="x", padx=12, pady=8)

        # 로그인 버튼
        login_btn = tk.Button(login_container, text="로그인", font=("Segoe UI", 12, "bold"),
                            command=self.login, bg="#F0B90B", fg="#000000",
                            relief="flat", cursor="hand2", bd=0, highlightthickness=0)
        login_btn.pack(fill="x", pady=(0, 10), ipady=10)

        # 회원가입 버튼
        register_btn = tk.Button(login_container, text="회원가입", font=("Segoe UI", 11),
                                command=self.open_register_window,
                                bg="#2B2F36", fg="#F0B90B", relief="flat",
                                cursor="hand2", bd=1, highlightthickness=0)
        register_btn.configure(highlightbackground="#F0B90B")
        register_btn.pack(fill="x", ipady=8)

        # 포커스 이벤트 추가 - 컨테이너 테두리 색상 변경
        def on_focus_in_username(event):
            username_container.config(highlightbackground="#F0B90B", highlightthickness=2)
        def on_focus_out_username(event):
            username_container.config(highlightbackground="#474D57", highlightthickness=1)
        def on_focus_in_password(event):
            password_container.config(highlightbackground="#F0B90B", highlightthickness=2)
        def on_focus_out_password(event):
            password_container.config(highlightbackground="#474D57", highlightthickness=1)

        # 초기 테두리 설정
        username_container.config(highlightbackground="#474D57", highlightthickness=1)
        password_container.config(highlightbackground="#474D57", highlightthickness=1)

        self.username.bind("<FocusIn>", on_focus_in_username)
        self.username.bind("<FocusOut>", on_focus_out_username)
        self.password.bind("<FocusIn>", on_focus_in_password)
        self.password.bind("<FocusOut>", on_focus_out_password)

        self.root.bind("<Return>", self.login)

    def create_input_field(self, parent, label_text, field_name, show=None):
        """입력 필드 생성"""
        field_frame = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        field_frame.pack(fill="x", pady=15)

        # 라벨을 중앙 정렬
        tk.Label(field_frame, text=label_text, font=("Segoe UI", 13, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(pady=(0, 8))

        # 입력 필드 컨테이너를 중앙에 배치
        entry_wrapper = tk.Frame(field_frame, bg=self.theme_colors["panel_bg"])
        entry_wrapper.pack()

        # 입력 필드를 감싸는 프레임으로 둥근 모서리 효과
        entry_container = tk.Frame(entry_wrapper, bg=self.theme_colors["input_bg"],
                                  relief="solid", borderwidth=1, highlightthickness=2,
                                  highlightcolor=self.theme_colors["primary"],
                                  highlightbackground=self.theme_colors["border"])
        entry_container.pack()

        entry = tk.Entry(entry_container, width=28, font=("Segoe UI", 13),
                        relief="flat", borderwidth=0, show=show,
                        bg=self.theme_colors["input_bg"], fg=self.theme_colors["text"],
                        insertbackground=self.theme_colors["text"], justify="center")
        entry.pack(padx=15, pady=12)

        # 포커스 이벤트로 테두리 색상 변경
        def on_focus_in(event):
            entry_container.config(highlightbackground=self.theme_colors["primary"])

        def on_focus_out(event):
            entry_container.config(highlightbackground=self.theme_colors["border"])

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

        setattr(self, field_name, entry)

    def create_binance_input_field(self, parent, label_text, field_name, show=None):
        """바이낸스 스타일 입력 필드 생성"""
        field_frame = tk.Frame(parent, bg="#2B2F36")
        field_frame.pack(fill="x", pady=(0, 20))

        # 라벨
        tk.Label(field_frame, text=label_text, font=("Segoe UI", 12),
                bg="#2B2F36", fg="#FFFFFF").pack(anchor="w", pady=(0, 8))

        # 입력 필드 컨테이너
        entry_container = tk.Frame(field_frame, bg="#1E2329", relief="solid", borderwidth=1,
                                  highlightthickness=1, highlightcolor="#F0B90B",
                                  highlightbackground="#474D57")
        entry_container.pack(fill="x")

        entry = tk.Entry(entry_container, font=("Segoe UI", 13), relief="flat", borderwidth=0,
                        bg="#1E2329", fg="#FFFFFF", insertbackground="#FFFFFF", show=show)
        entry.pack(fill="x", padx=16, pady=10)  # pady를 16에서 10으로 줄임

        # 포커스 이벤트
        def on_focus_in(event):
            entry_container.config(highlightbackground="#F0B90B")

        def on_focus_out(event):
            entry_container.config(highlightbackground="#474D57")

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

        setattr(self, field_name, entry)

    def create_info_area(self, parent):
        """우측 정보 영역 생성"""
        info_container = tk.Frame(parent, bg=self.theme_colors["bg"], width=350)
        info_container.pack(side="right", fill="y", padx=(0, 20), pady=20)
        info_container.pack_propagate(False)

        # 공지사항 카드
        notice_card = tk.Frame(info_container, bg=self.theme_colors["panel_bg"],
                              relief="solid", borderwidth=1)
        notice_card.pack(fill="both", expand=True, pady=(0, 10))

        notice_header = tk.Frame(notice_card, bg=self.theme_colors["primary"], height=40)
        notice_header.pack(fill="x")
        notice_header.pack_propagate(False)

        tk.Label(notice_header, text="공지사항", font=("Segoe UI", 12, "bold"),
                bg=self.theme_colors["primary"], fg=self.theme_colors["text_white"]).pack(pady=10)

        notice_box = tk.Text(notice_card, wrap="word", font=("Segoe UI", 10),
                            bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                            relief="flat", borderwidth=0)
        notice_box.insert(tk.END, "공지사항 불러오는 중...")
        notice_box.configure(state="disabled")
        notice_box.pack(expand=True, fill="both", padx=15, pady=15)

        self.load_notice_text(notice_box)
        
    def toggle_theme(self):
        """테마 전환 (라이트 <-> 다크)"""
        global CURRENT_THEME
        new_theme = "dark" if self.theme == "light" else "light"
        self.theme = new_theme
        CURRENT_THEME = new_theme
        self.theme_colors = THEME_COLORS[self.theme]

        # 전체 UI 다시 그리기
        self.refresh_ui()

    def refresh_ui(self):
        """UI 전체 새로고침"""
        # 기존 위젯들 제거
        for widget in self.root.winfo_children():
            widget.destroy()

        # UI 다시 생성
        self.root.configure(bg=self.theme_colors["bg"])

        # 상단 헤더 바
        self.create_header()

        # 메인 컨테이너
        main_container = tk.Frame(self.root, bg=self.theme_colors["bg"])
        main_container.pack(fill="both", expand=True, padx=0, pady=0)

        # 중앙 로그인 영역
        self.create_login_area(main_container)

        # 우측 공지사항 및 차트 영역
        self.create_info_area(main_container)

    def check_update_prompt(self):
        is_new, data = check_new_version()
        if is_new:
            if messagebox.askyesno("🔔 업데이트 알림", "새 버전이 있습니다.\n지금 업데이트 하시겠습니까?"):
                perform_update(data)

    def load_notice_text(self, notice_box: tk.Text):
        try:
            res = requests.get(f"{API_BASE_URL}/notice", headers=API_HEADERS, timeout=5)
            if res.status_code == 200:
                content = res.json().get("notice", "공지사항이 없습니다.")
            elif res.status_code == 404:
                content = "[공지사항 없음] 서버에 notice.txt 파일이 존재하지 않습니다."
            else:
                content = f"서버 오류: {res.status_code}"
        except Exception as e:
            content = f"공지사항 로드 실패: {e}"

        notice_box.config(state="normal")
        notice_box.delete("1.0", tk.END)
        notice_box.insert(tk.END, content)
        notice_box.config(state="disabled")


    def login(self,event=None):
        user = self.username.get().strip()
        pwd = self.password.get().strip()

        if not user or not pwd:
            messagebox.showerror("로그인 실패", "아이디와 비밀번호를 모두 입력해주세요.")
            return

        try:
            res = requests.get(f"{API_BASE_URL}/admin/user/{user}", headers=API_HEADERS)
            if res.status_code == 200:
                data = res.json()
                if data.get("password") == pwd:
                    self.root.destroy()
                    open_dashboard(user)
                else:
                    messagebox.showerror("로그인 실패", "비밀번호가 올바르지 않습니다.")
            else:
                messagebox.showerror("로그인 실패", "존재하지 않는 아이디입니다.")
        except Exception as e:
            messagebox.showerror("오류", f"서버 요청 실패: {e}")
            
    def open_register_window(self):
        reg_win = tk.Toplevel(self.root)
        reg_win.title("회원가입")
        reg_win.geometry("500x700")
        reg_win.configure(bg="#181A20")
        reg_win.resizable(False, False)

        # 창을 중앙에 배치
        reg_win.transient(self.root)
        reg_win.grab_set()

        # 메인 컨테이너
        main_container = tk.Frame(reg_win, bg="#181A20")
        main_container.pack(fill="both", expand=True, padx=40, pady=30)

        # 회원가입 카드
        register_card = tk.Frame(main_container, bg="#2B2F36", relief="flat", borderwidth=0)
        register_card.pack(fill="both", expand=True)

        # 스크롤 가능한 영역 생성
        canvas = tk.Canvas(register_card, bg="#2B2F36", highlightthickness=0)
        scrollbar = tk.Scrollbar(register_card, orient="vertical", command=canvas.yview,
                                bg="#2B2F36", troughcolor="#2B2F36", activebackground="#F0B90B")
        scrollable_frame = tk.Frame(canvas, bg="#2B2F36")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 스크롤 영역 배치
        canvas.pack(side="left", fill="both", expand=True)
        # scrollbar.pack(side="right", fill="y")  # 스크롤바 숨김

        # 내부 컨테이너
        inner_container = tk.Frame(scrollable_frame, bg="#2B2F36")
        inner_container.pack(fill="both", expand=True, padx=30, pady=20)  # 패딩 줄임

        # 로고 및 제목
        logo_frame = tk.Frame(inner_container, bg="#2B2F36")
        logo_frame.pack(fill="x", pady=(0, 10))  # 간격 줄임

        logo_label = tk.Label(logo_frame, text="⬢", font=("Segoe UI", 18, "bold"),  # 크기 줄임
                             bg="#2B2F36", fg="#F0B90B")
        logo_label.pack(side="left")

        brand_label = tk.Label(logo_frame, text="COINPILOT", font=("Segoe UI", 14, "bold"),  # 크기 줄임
                              bg="#2B2F36", fg="#FFFFFF")
        brand_label.pack(side="left", padx=(6, 0))

        # 제목
        title = tk.Label(inner_container, text="Create Account", font=("Segoe UI", 24, "bold"),  # 크기 줄임
                        bg="#2B2F36", fg="#FFFFFF")
        title.pack(anchor="w", pady=(10, 20))  # 간격 줄임

        # 입력 필드 생성 함수 - 간격 줄임
        def create_register_field(parent, label_text, show=None):
            field_frame = tk.Frame(parent, bg="#2B2F36")
            field_frame.pack(fill="x", pady=(0, 10))  # 20에서 10으로 줄임

            tk.Label(field_frame, text=label_text, font=("Segoe UI", 12),
                    bg="#2B2F36", fg="#FFFFFF").pack(anchor="w", pady=(0, 5))  # 8에서 5로 줄임

            entry_container = tk.Frame(field_frame, bg="#1E2329", relief="flat", borderwidth=0,
                                      highlightthickness=1, highlightcolor="#F0B90B",
                                      highlightbackground="#474D57")
            entry_container.pack(fill="x")

            entry = tk.Entry(entry_container, font=("Segoe UI", 13), relief="flat", borderwidth=0,
                            bg="#1E2329", fg="#FFFFFF", insertbackground="#FFFFFF", show=show)
            entry.pack(fill="x", padx=12, pady=8)  # 패딩 줄임

            # 포커스 이벤트
            def on_focus_in(event):
                entry_container.config(highlightbackground="#F0B90B", highlightthickness=2)

            def on_focus_out(event):
                entry_container.config(highlightbackground="#474D57", highlightthickness=1)

            entry.bind("<FocusIn>", on_focus_in)
            entry.bind("<FocusOut>", on_focus_out)

            return entry

        # 마우스 휠 스크롤 바인딩
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # 입력 필드들
        api_key_entry = create_register_field(inner_container, "API Key")
        secret_key_entry = create_register_field(inner_container, "Secret Key", show="*")

        # API 키 인증 버튼
        api_verify_btn = tk.Button(inner_container, text="API 키 인증", font=("Segoe UI", 12, "bold"),
                                  bg="#F0B90B", fg="#000000", relief="flat", cursor="hand2",
                                  bd=0, highlightthickness=0)
        api_verify_btn.pack(fill="x", pady=(0, 10), ipady=10)  # 간격과 높이 줄임

        username_entry = create_register_field(inner_container, "아이디")

        # 아이디 중복 확인 버튼
        username_check_btn = tk.Button(inner_container, text="아이디 중복 확인", font=("Segoe UI", 12, "bold"),
                                      bg="#2B2F36", fg="#F0B90B", relief="flat", cursor="hand2",
                                      bd=1, highlightthickness=0)
        username_check_btn.configure(highlightbackground="#F0B90B")
        username_check_btn.pack(fill="x", pady=(0, 10), ipady=10)  # 간격과 높이 줄임

        password_entry = create_register_field(inner_container, "비밀번호", show="*")

        def check_username_duplicate():
            username = username_entry.get().strip()
            if not username:
                messagebox.showwarning("입력 오류", "아이디를 입력하세요.")
                self.username_checked = False
                return

            try:
                res = requests.get(f"{API_BASE_URL}/admin/user/{username}", headers=API_HEADERS)
                if res.status_code == 200:
                    messagebox.showerror("중복 아이디", "❌ 이미 사용 중인 아이디입니다.")
                    self.username_checked = False
                elif res.status_code == 404:
                    messagebox.showinfo("사용 가능", "✅ 사용 가능한 아이디입니다.")
                    self.username_checked = True
                else:
                    messagebox.showerror("서버 오류", f"요청 실패: {res.status_code}")
                    self.username_checked = False
            except Exception as e:
                logging.exception("아이디 중복 확인 실패")
                messagebox.showerror("오류", f"아이디 중복 확인 중 오류 발생: {e}")
                self.username_checked = False

        def on_verify_api_key():
            key = api_key_entry.get().strip()
            sec = secret_key_entry.get().strip()
            if not key or not sec:
                messagebox.showerror("입력 오류", "API Key와 Secret Key를 모두 입력해 주세요.")
                return

            success,code = verify_api_whitelist(key, sec)
            if success:
                self.api_verified = True
                messagebox.showinfo("성공", "✅ 이 서버의 IP(*************)가 Binance API 키에 등록되어 있습니다.")
            elif code == 409:
                messagebox.showerror("실패", "❌ 중복 등록.\n이미 해당 api가 등록되어 있습니다.")
            else:
                messagebox.showerror("실패", "❌ API 인증 실패.\nIP 화이트리스트에 *************이 등록되었는지 확인해 주세요.")

        # 버튼 이벤트 연결
        api_verify_btn.config(command=on_verify_api_key)
        username_check_btn.config(command=check_username_duplicate)

        def create_default_settings(username):
            """처음 계정 생성 시 비어있는 전략 골격 JSON을 생성하고 서버에 등록"""
            default_conf = {
                "username": username,
                "strategy_type": "grid",
                "grid": {
                    "coins": []
                },
                "cycle": {
                    "coins": []
                }
            }

            # 🔥 서버에도 설정 등록
            try:
                res = requests.post(f"{API_BASE_URL}/settings", json=default_conf, headers=API_HEADERS)
                if res.status_code != 200:
                    print(f"[경고] 서버에 기본 설정 등록 실패: {res.text}")
            except Exception as e:
                print(f"[오류] 서버 요청 실패: {e}")

            return default_conf

        # 등록 버튼
        def on_register():
            can_register = (
                self.api_verified and
                self.username_checked and
                password_entry.get().strip() != ""
            )
            if not can_register:
                messagebox.showerror("입력 오류", "API키 인증, Secert키 인증, 아이디 중복 확인, 비밀번호 입력을 완료하셔야 합니다.")
                return
            data = {
                "username": username_entry.get().strip(),
                "password": password_entry.get().strip(),
                "api_key": api_key_entry.get().strip(),
                "secret_key": secret_key_entry.get().strip(),
                "role": "user"
            }
            if not all(data.values()):
                messagebox.showerror("입력 오류", "모든 항목을 입력해야 합니다.")
                return

            try:
                res = requests.post(f"{API_BASE_URL}/admin/register", json=data, headers=API_HEADERS)
                if res.status_code == 200:
                    messagebox.showinfo("성공", "회원가입이 완료되었습니다.")
                    reg_win.destroy()
                    create_default_settings(data["username"])
                else:
                    messagebox.showerror("실패", f"회원가입 실패: {res.text}")
            except Exception as e:
                logging.exception("회원가입 실패")
                messagebox.showerror("오류", f"요청 실패: {e}")

        # 버튼 영역
        button_frame = tk.Frame(inner_container, bg="#2B2F36")
        button_frame.pack(fill="x", pady=(20, 0))  # 간격 줄임

        # 회원가입 완료 버튼
        register_btn = tk.Button(button_frame, text="회원가입 완료", font=("Segoe UI", 13, "bold"),  # 폰트 크기 줄임
                                bg="#F0B90B", fg="#000000", relief="flat", cursor="hand2",
                                bd=0, highlightthickness=0, command=on_register)
        register_btn.pack(fill="x", pady=(0, 8), ipady=12)  # 간격과 높이 줄임

        # 구분선
        separator_frame = tk.Frame(button_frame, bg="#2B2F36")
        separator_frame.pack(fill="x", pady=8)  # 간격 줄임

        tk.Label(separator_frame, text="or", font=("Segoe UI", 11),  # 폰트 크기 줄임
                bg="#2B2F36", fg="#848E9C").pack()

        # 닫기 버튼
        close_btn = tk.Button(button_frame, text="닫기", font=("Segoe UI", 11),  # 폰트 크기 줄임
                             bg="#2B2F36", fg="#F0B90B", relief="flat", cursor="hand2",
                             bd=1, highlightthickness=0, command=reg_win.destroy)
        close_btn.configure(highlightbackground="#F0B90B")
        close_btn.pack(fill="x", ipady=10)  # 높이 줄임

        # 스크롤 영역 업데이트
        inner_container.update_idletasks()
        canvas.configure(scrollregion=canvas.bbox("all"))

class ChartManager:
    """개선된 차트 관리 클래스 - HTML 파일 생성 없이 동적 차트 관리"""

    def __init__(self):
        self.current_widget = None
        self.chart_html_template = self._create_chart_template()
        self.temp_html_path = None

    def _create_chart_template(self):
        """재사용 가능한 차트 HTML 템플릿 생성"""
        return '''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>CoinPilot Chart</title>
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #0d1117;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        #tv_chart_container {
            height: 100vh;
            width: 100%;
            background-color: #0d1117;
        }
        .loading-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            flex-direction: column;
            color: #8b949e;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #21262d;
            border-top: 3px solid #f0b90b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-container {
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            color: #f85149;
            text-align: center;
            padding: 20px;
        }
        .retry-btn {
            background: #f0b90b;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 15px;
            font-weight: bold;
        }
        .retry-btn:hover {
            background: #d4a017;
        }
    </style>
</head>
<body>
    <div id="tv_chart_container">
        <div class="loading-container" id="loading">
            <div class="loading-spinner"></div>
            <div>차트 로딩 중...</div>
        </div>
    </div>

    <div class="error-container" id="error">
        <div style="font-size: 18px; margin-bottom: 10px;">⚠️ 차트 로딩 실패</div>
        <div style="font-size: 14px; margin-bottom: 5px;">인터넷 연결을 확인해주세요</div>
        <button class="retry-btn" onclick="loadChart(currentSymbol)">다시 시도</button>
        <button class="retry-btn" onclick="openExternalChart()" style="margin-left: 10px;">외부 차트 열기</button>
    </div>

    <script type="text/javascript">
        let currentSymbol = 'BTCUSDT';
        let widget = null;
        let retryCount = 0;
        const maxRetries = 3;

        function showLoading() {
            document.getElementById('loading').style.display = 'flex';
            document.getElementById('error').style.display = 'none';
            document.getElementById('tv_chart_container').style.display = 'block';
        }

        function showError() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'flex';
            document.getElementById('tv_chart_container').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'none';
        }

        function loadChart(symbol) {
            if (!symbol) symbol = 'BTCUSDT';
            currentSymbol = symbol.toUpperCase();

            showLoading();

            // 기존 위젯 제거
            if (widget) {
                try {
                    widget.remove();
                } catch (e) {
                    console.log('위젯 제거 중 오류:', e);
                }
                widget = null;
            }

            // 컨테이너 초기화
            const container = document.getElementById('tv_chart_container');
            const loadingDiv = document.getElementById('loading');
            container.innerHTML = '';
            container.appendChild(loadingDiv);

            // 새 차트 컨테이너 생성
            const chartDiv = document.createElement('div');
            chartDiv.id = 'tradingview_widget';
            chartDiv.style.height = '100%';
            chartDiv.style.width = '100%';
            container.appendChild(chartDiv);

            try {
                // TradingView 위젯 생성
                widget = new TradingView.widget({
                    width: "100%",
                    height: "100%",
                    symbol: `BINANCE:${currentSymbol}`,
                    interval: "15",
                    timezone: "Asia/Seoul",
                    theme: "dark",
                    style: "1",
                    locale: "kr",
                    toolbar_bg: "#21262d",
                    enable_publishing: false,
                    hide_top_toolbar: false,
                    hide_legend: false,
                    save_image: false,
                    container_id: "tradingview_widget",
                    autosize: true,
                    onChartReady: function() {
                        console.log('차트 로딩 완료:', currentSymbol);
                        hideLoading();
                        retryCount = 0;
                    }
                });

                // 타임아웃 설정 (10초)
                setTimeout(() => {
                    if (document.getElementById('loading').style.display !== 'none') {
                        console.log('차트 로딩 타임아웃');
                        if (retryCount < maxRetries) {
                            retryCount++;
                            console.log(`재시도 ${retryCount}/${maxRetries}`);
                            loadChart(currentSymbol);
                        } else {
                            showError();
                        }
                    }
                }, 10000);

            } catch (error) {
                console.error('TradingView 위젯 생성 실패:', error);
                if (retryCount < maxRetries) {
                    retryCount++;
                    setTimeout(() => loadChart(currentSymbol), 2000);
                } else {
                    showError();
                }
            }
        }

        function changeSymbol(symbol) {
            loadChart(symbol);
        }

        function openExternalChart() {
            window.open(`https://www.tradingview.com/chart/?symbol=BINANCE:${currentSymbol}`, '_blank');
        }

        // 페이지 로드 시 기본 차트 로딩
        window.addEventListener('load', () => {
            loadChart('BTCUSDT');
        });

        // 외부에서 호출 가능한 함수들을 전역으로 노출
        window.loadChart = loadChart;
        window.changeSymbol = changeSymbol;
        window.openExternalChart = openExternalChart;
    </script>
</body>
</html>'''

    def get_chart_html(self, symbol="BTCUSDT"):
        """심볼에 관계없이 동일한 HTML 반환 (JavaScript에서 동적 처리)"""
        return self.chart_html_template

    def cleanup(self):
        """임시 파일 정리"""
        if self.temp_html_path and os.path.exists(self.temp_html_path):
            try:
                os.remove(self.temp_html_path)
                self.temp_html_path = None
            except:
                pass

class StrategyGUI:
    def __init__(self, root, username):
        self.root = root
        self.username = username
        self.root.title(f"CoinPilot Pro {CURRENT_VERSION} - {username}")
        self.root.geometry("1600x900")  # 1400 -> 1600으로 증가
        self.root.protocol("WM_DELETE_WINDOW", self.confirm_exit)

        # 테마 색상 초기화
        self.theme = CURRENT_THEME
        self.theme_colors = THEME_COLORS[self.theme]

        # 창을 화면 중앙에 배치하고 최대화
        self.center_window()
        self.root.state('zoomed')  # 창 최대화
        self.root.configure(bg=self.theme_colors["bg"])

        # 상태 변수들
        self.strategy_type = "grid"
        self.is_running = False
        self.coin_frames = []
        self.coin_entries = []

        # 창 관리 변수들
        self.log_window = None  # 로그 창 참조

        # 차트 관리자 초기화
        self.chart_manager = ChartManager()
        self.current_chart_symbol = "BTCUSDT"

        # UI 구성
        self.build_modern_layout()
        self.load_settings()
        if not self.coin_entries:
            self.add_coin_row()

        # 기본 차트 표시
        self.open_chart("BTCUSDT")

        self.start_auto_update(interval=1.0)

        # 창이 완전히 로드된 후 포지션 테이블 너비 계산
        def trigger_width_calculation():
            if hasattr(self, 'position_table'):
                self.position_table.update_column_widths()

        # 여러 시점에서 너비 계산 트리거
        self.root.after(100, trigger_width_calculation)
        self.root.after(500, trigger_width_calculation)
        self.root.after(1000, trigger_width_calculation)

    def center_window(self):
        """창을 화면 중앙에 배치"""
        self.root.update_idletasks()
        width = 1600  # 1400 -> 1600으로 증가
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def open_chart(self, symbol):
        """차트 표시 - 임시 비활성화"""
        return  # 차트 기능 임시 비활성화

    def _show_chart_options(self, symbol):
        """차트 표시 옵션 제공"""
        # 기존 차트 내용 제거
        for widget in self.chart_content.winfo_children():
            widget.destroy()

        # 차트 옵션 UI 생성
        options_frame = tk.Frame(self.chart_content, bg=self.theme_colors["bg"])
        options_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 제목
        title_label = tk.Label(options_frame,
                              text=f"{symbol} 차트",
                              font=("Segoe UI", 18, "bold"),
                              bg=self.theme_colors["bg"],
                              fg=self.theme_colors["primary"])
        title_label.pack(pady=(0, 30))

        # 설명
        desc_label = tk.Label(options_frame,
                             text="차트를 보는 방법을 선택하세요:",
                             font=("Segoe UI", 12),
                             bg=self.theme_colors["bg"],
                             fg=self.theme_colors["text"])
        desc_label.pack(pady=(0, 20))

        # 버튼 프레임
        button_frame = tk.Frame(options_frame, bg=self.theme_colors["bg"])
        button_frame.pack(pady=20)

        # TradingView 버튼
        tv_btn = tk.Button(button_frame,
                          text="📈 TradingView에서 보기",
                          font=("Segoe UI", 12, "bold"),
                          bg=self.theme_colors["primary"],
                          fg="#000000",
                          relief="flat",
                          padx=20, pady=10,
                          command=lambda: self._open_external_chart(symbol, "tradingview"))
        tv_btn.pack(pady=10, fill="x")

        # 바이낸스 버튼
        binance_btn = tk.Button(button_frame,
                               text="🔶 바이낸스에서 보기",
                               font=("Segoe UI", 12, "bold"),
                               bg=self.theme_colors["warning"],
                               fg="#000000",
                               relief="flat",
                               padx=20, pady=10,
                               command=lambda: self._open_external_chart(symbol, "binance"))
        binance_btn.pack(pady=10, fill="x")

        # 임베드 차트 버튼 (실험적)
        embed_btn = tk.Button(button_frame,
                             text="🖥️ 앱 내에서 보기 (실험적)",
                             font=("Segoe UI", 12),
                             bg=self.theme_colors["panel_bg"],
                             fg=self.theme_colors["text"],
                             relief="flat",
                             padx=20, pady=10,
                             command=lambda: self._create_embedded_chart(symbol))
        embed_btn.pack(pady=10, fill="x")

        # 정보 라벨
        info_label = tk.Label(options_frame,
                             text="💡 외부 사이트에서 보는 것이 가장 안정적입니다",
                             font=("Segoe UI", 10),
                             bg=self.theme_colors["bg"],
                             fg=self.theme_colors["text_light"])
        info_label.pack(pady=(20, 0))

    def _open_external_chart(self, symbol, platform):
        """외부 차트 사이트 열기"""

        if platform == "tradingview":
            url = f"https://www.tradingview.com/chart/?symbol=BINANCE:{symbol}"
        elif platform == "binance":
            url = f"https://www.binance.com/en/trade/{symbol}"
        else:
            return

        try:
            webbrowser.open(url)

            # 성공 메시지 표시
            success_frame = tk.Frame(self.chart_content, bg=self.theme_colors["bg"])
            success_frame.pack(fill="both", expand=True)

            success_label = tk.Label(success_frame,
                                   text=f"✅ {platform.title()} 차트가 브라우저에서 열렸습니다",
                                   font=("Segoe UI", 14),
                                   bg=self.theme_colors["bg"],
                                   fg=self.theme_colors["success"])
            success_label.pack(expand=True)

        except Exception as e:
            messagebox.showerror("오류", f"차트를 열 수 없습니다: {e}")

    def _create_embedded_chart(self, symbol):
        """임베드 차트 생성 (실험적)"""
        try:
            # 기존 차트 내용 제거
            self._safely_destroy_chart_widgets()

            # 짧은 딜레이 후 차트 생성
            self.chart_content.after(200, lambda: self._create_new_chart_safe(symbol))

        except Exception as e:
            self.create_chart_ui(symbol)

    def _safely_destroy_chart_widgets(self):
        """차트 위젯들을 안전하게 파괴"""
        try:
            # 기존 차트 위젯이 있으면 안전하게 제거
            if hasattr(self, '_chart_web_frame') and self._chart_web_frame:
                try:
                    # 위젯이 아직 존재하는지 확인 후 파괴
                    if self._chart_web_frame.winfo_exists():
                        self._chart_web_frame.destroy()

                except Exception as e:
                    pass  # 오류 무시
                finally:
                    # 참조 제거
                    self._chart_web_frame = None

            # 차트 컨테이너의 모든 자식 위젯 제거
            for widget in list(self.chart_content.winfo_children()):
                try:
                    if widget.winfo_exists():
                        widget.destroy()
                except Exception as e:
                    pass  # 오류 무시

        except Exception as e:
            pass  # 오류 무시

    def _create_new_chart_safe(self, symbol):
        """안전한 차트 생성 (오류 처리 강화)"""
        try:
            self.current_chart_symbol = symbol.upper()
            self._create_new_chart()
        except Exception as e:
            self.create_chart_ui(symbol)

    def _create_new_chart(self):
        """새로운 차트 위젯 생성 - 심볼별 HTML 생성"""
        try:
            import tkinterweb
            print(f"새 차트 생성 시작: {self.current_chart_symbol}")

            # 심볼별 HTML 내용 생성 (동적으로)
            html_content = self._generate_symbol_chart_html(self.current_chart_symbol)

            # 임시 파일에 저장
            import tempfile
            import os

            # 기존 파일이 있으면 삭제
            if hasattr(self, '_chart_html_file') and os.path.exists(self._chart_html_file):
                try:
                    os.remove(self._chart_html_file)
                except:
                    pass

            # 새 임시 파일 생성
            fd, self._chart_html_file = tempfile.mkstemp(suffix='.html', prefix='coinpilot_chart_')
            os.close(fd)

            # HTML 내용 작성
            with open(self._chart_html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"차트 HTML 파일 생성: {self._chart_html_file}")

            # 웹뷰 프레임 생성 (안전하게)
            try:
                self._chart_web_frame = tkinterweb.HtmlFrame(
                    self.chart_content,
                    messages_enabled=False
                )

                # HTML 파일 로드
                self._chart_web_frame.load_file(self._chart_html_file)
                self._chart_web_frame.pack(fill="both", expand=True)

                print(f"새 차트 생성 완료: {self.current_chart_symbol}")

                # 차트 로딩 상태 확인 (5초 후)
                def check_chart_loaded():
                    try:
                        if hasattr(self, '_chart_web_frame') and self._chart_web_frame and self._chart_web_frame.winfo_exists():
                            print(f"차트 로딩 상태 확인 완료: {self.current_chart_symbol}")
                    except Exception as e:
                        print(f"차트 상태 확인 중 오류: {e}")

                self.chart_content.after(5000, check_chart_loaded)

            except Exception as e:
                print(f"웹뷰 프레임 생성 실패: {e}")
                raise e

        except ImportError:
            print("tkinterweb 라이브러리가 설치되지 않았습니다")
            self.create_chart_ui(self.current_chart_symbol)
        except Exception as e:
            print(f"새 차트 생성 실패: {e}")
            import traceback
            traceback.print_exc()
            self.create_chart_ui(self.current_chart_symbol)

    def _generate_symbol_chart_html(self, symbol):
        """특정 심볼에 대한 간단하고 안정적인 차트 HTML 생성"""
        return f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{symbol} Chart</title>
    <style>
        body {{
            margin: 0;
            padding: 0;
            background-color: #0d1117;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }}
        .chart-container {{
            height: 100vh;
            width: 100%;
            background-color: #0d1117;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }}
        .loading-spinner {{
            width: 50px;
            height: 50px;
            border: 4px solid #21262d;
            border-top: 4px solid #f0b90b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }}
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        .chart-info {{
            color: #8b949e;
            text-align: center;
            margin-bottom: 20px;
        }}
        .chart-title {{
            font-size: 24px;
            font-weight: bold;
            color: #f0b90b;
            margin-bottom: 10px;
        }}
        .chart-subtitle {{
            font-size: 14px;
            margin-bottom: 20px;
        }}
        .external-link {{
            background: #f0b90b;
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }}
        .external-link:hover {{
            background: #d4a017;
        }}
        .tradingview-widget {{
            width: 100%;
            height: 100vh;
            border: none;
        }}
        #widget-container {{
            width: 100%;
            height: 100vh;
            display: none;
        }}
        .loading-container {{
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }}
    </style>
</head>
<body>
    <div class="loading-container" id="loading">
        <div class="loading-spinner"></div>
        <div class="chart-info">
            <div class="chart-title">{symbol}</div>
            <div class="chart-subtitle">TradingView 차트 로딩 중...</div>
        </div>
        <a href="https://www.tradingview.com/chart/?symbol=BINANCE:{symbol}"
           target="_blank" class="external-link">
            TradingView에서 보기
        </a>
        <a href="https://www.binance.com/en/trade/{symbol}"
           target="_blank" class="external-link">
            바이낸스에서 보기
        </a>
    </div>

    <div id="widget-container">
        <!-- TradingView Widget BEGIN -->
        <div class="tradingview-widget-container" style="height:100vh;width:100%">
            <div class="tradingview-widget-container__widget" style="height:100vh;width:100%"></div>
            <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js" async>
            {{
                "autosize": true,
                "symbol": "BINANCE:{symbol}",
                "interval": "15",
                "timezone": "Asia/Seoul",
                "theme": "dark",
                "style": "1",
                "locale": "kr",
                "enable_publishing": false,
                "backgroundColor": "rgba(13, 17, 23, 1)",
                "gridColor": "rgba(33, 38, 45, 1)",
                "hide_top_toolbar": false,
                "hide_legend": false,
                "save_image": false,
                "calendar": false,
                "support_host": "https://www.tradingview.com"
            }}
            </script>
        </div>
        <!-- TradingView Widget END -->
    </div>

    <script type="text/javascript">
        // 3초 후 위젯 표시 (로딩 시간 확보)
        setTimeout(function() {{
            document.getElementById('loading').style.display = 'none';
            document.getElementById('widget-container').style.display = 'block';
        }}, 3000);

        // 10초 후에도 로딩 중이면 외부 링크 강조
        setTimeout(function() {{
            const loading = document.getElementById('loading');
            if (loading.style.display !== 'none') {{
                loading.innerHTML = `
                    <div class="chart-info">
                        <div class="chart-title">{symbol}</div>
                        <div class="chart-subtitle">차트 로딩이 지연되고 있습니다</div>
                        <div style="margin: 20px 0; color: #f85149;">
                            인터넷 연결을 확인하거나 아래 링크를 이용해주세요
                        </div>
                    </div>
                    <a href="https://www.tradingview.com/chart/?symbol=BINANCE:{symbol}"
                       target="_blank" class="external-link">
                        TradingView에서 보기
                    </a>
                    <a href="https://www.binance.com/en/trade/{symbol}"
                       target="_blank" class="external-link">
                        바이낸스에서 보기
                    </a>
                `;
            }}
        }}, 10000);
    </script>
</body>
</html>'''



    def create_chart_ui(self, symbol):
        """tkinter 위젯으로 차트 UI 생성"""
        # 메인 컨테이너
        main_frame = tk.Frame(self.chart_content, bg=self.theme_colors["panel_bg"])
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 심볼 제목
        title_frame = tk.Frame(main_frame, bg=self.theme_colors["panel_bg"])
        title_frame.pack(fill="x", pady=(0, 20))

        tk.Label(title_frame, text=symbol, font=("Segoe UI", 24, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["primary"]).pack(side="left")

        tk.Label(title_frame, text="BINANCE", font=("Segoe UI", 12),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text_light"]).pack(side="left", padx=(15, 0))

        # 차트 플레이스홀더
        chart_frame = tk.Frame(main_frame, bg=self.theme_colors["hover"], relief="solid", borderwidth=2)
        chart_frame.pack(fill="both", expand=True, pady=20)

        # 차트 아이콘과 텍스트
        icon_frame = tk.Frame(chart_frame, bg=self.theme_colors["hover"])
        icon_frame.pack(expand=True)

        tk.Label(icon_frame, text="📊", font=("Segoe UI", 48),
                bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(pady=(50, 10))

        tk.Label(icon_frame, text=f"{symbol} 차트", font=("Segoe UI", 20, "bold"),
                bg=self.theme_colors["hover"], fg=self.theme_colors["primary"]).pack(pady=(0, 10))

        tk.Label(icon_frame, text="TradingView 위젯 로딩 실패", font=("Segoe UI", 14),
                bg=self.theme_colors["hover"], fg=self.theme_colors["text_light"]).pack()

        tk.Label(icon_frame, text="외부 차트를 이용해주세요", font=("Segoe UI", 14),
                bg=self.theme_colors["hover"], fg=self.theme_colors["text_light"]).pack(pady=(0, 30))

        # 버튼들
        button_frame = tk.Frame(icon_frame, bg=self.theme_colors["hover"])
        button_frame.pack(pady=20)

        def open_tradingview():
            import webbrowser
            webbrowser.open(f"https://www.tradingview.com/chart/?symbol=BINANCE:{symbol}")
            print(f"TradingView에서 차트 열기: {symbol}")

        def open_binance():
            import webbrowser
            webbrowser.open(f"https://www.binance.com/en/trade/{symbol}")
            print(f"Binance에서 거래 페이지 열기: {symbol}")

        # TradingView 버튼
        tv_btn = tk.Button(button_frame, text="TradingView에서 열기", font=("Segoe UI", 12, "bold"),
                          bg=self.theme_colors["long"], fg=self.theme_colors["text_white"],
                          relief="flat", padx=20, pady=10, cursor="hand2", bd=0,
                          highlightthickness=0, command=open_tradingview)
        tv_btn.pack(side="left", padx=(0, 10))

        # Binance 버튼
        binance_btn = tk.Button(button_frame, text="Binance에서 거래", font=("Segoe UI", 12, "bold"),
                               bg=self.theme_colors["secondary"], fg=self.theme_colors["text_white"],
                               relief="flat", padx=20, pady=10, cursor="hand2", bd=0,
                               highlightthickness=0, command=open_binance)
        binance_btn.pack(side="left")

        # 정보 테이블
        info_frame = tk.Frame(main_frame, bg=self.theme_colors["panel_bg"])
        info_frame.pack(fill="x", pady=(20, 0))

        # 정보 항목들
        info_items = [
            ("거래소", "Binance"),
            ("심볼", symbol),
            ("상태", "활성"),
            ("차트 타입", "캔들스틱")
        ]

        for i, (label, value) in enumerate(info_items):
            row = i // 2
            col = i % 2

            item_frame = tk.Frame(info_frame, bg=self.theme_colors["hover"], relief="solid", borderwidth=1)
            item_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")

            tk.Label(item_frame, text=label, font=("Segoe UI", 10),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text_light"]).pack(anchor="w", padx=15, pady=(10, 0))

            tk.Label(item_frame, text=value, font=("Segoe UI", 12, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(anchor="w", padx=15, pady=(0, 10))

        # 그리드 컬럼 설정
        info_frame.grid_columnconfigure(0, weight=1)
        info_frame.grid_columnconfigure(1, weight=1)



    def cleanup_chart_resources(self):
        """차트 관련 리소스 정리"""
        try:
            # 차트 위젯 안전하게 파괴
            self._safely_destroy_chart_widgets()

            # 임시 HTML 파일 정리
            if hasattr(self, '_chart_html_file') and os.path.exists(self._chart_html_file):
                try:
                    os.remove(self._chart_html_file)
                    print(f"임시 차트 파일 삭제: {self._chart_html_file}")
                except Exception as e:
                    print(f"임시 파일 삭제 실패: {e}")

            # 차트 매니저 정리
            if hasattr(self, 'chart_manager'):
                self.chart_manager.cleanup()

        except Exception as e:
            print(f"차트 리소스 정리 실패: {e}")

    def confirm_exit(self):
        """프로그램 종료 시 확인 및 정리"""
        if messagebox.askokcancel("종료", "정말로 종료하시겠습니까?"):
            try:
                # 차트 리소스 정리
                self.cleanup_chart_resources()

                # 기타 리소스 정리
                if hasattr(self, 'update_job') and self.update_job:
                    self.root.after_cancel(self.update_job)

            except Exception as e:
                print(f"종료 시 정리 중 오류: {e}")
            finally:
                self.root.destroy()

    def _remove_coin_row_by_frame(self, frame):
        if frame in self.coin_frames:
            idx = self.coin_frames.index(frame)
            frame.destroy()
            self.coin_frames.pop(idx)
            self.coin_entries.pop(idx)

    def show_cycle_fields(self):
        for frame in self.coin_frames:
            frame.pack_forget()
        self.coin_frames.clear()
        self.coin_entries.clear()
         
        self.add_coin_row(strategy_type="cycle")  # 순환매수용 행 추

    def show_grid_fields(self):
        for frame in self.coin_frames:
            frame.pack_forget()
        self.coin_frames.clear()
        self.coin_entries.clear()
         
        self.add_coin_row(strategy_type="grid")

    def on_strategy_type_change(self, event=None):

        selected = self.strategy_type_var.get()
        self.strategy_type = "grid" if selected == "거미줄 매매법" else "cycle"

        selected_strategy = "grid" if self.strategy_type_var.get() == "거미줄 매매법" else "cycle"

        try:
            # ✅ 무조건 서버로부터 최신 settings를 다시 받아온다
            res = requests.get(f"{API_BASE_URL}/settings/{self.username}", headers=API_HEADERS)
            if res.status_code == 200:
                data = res.json()

                # 서버에서 받아온 fresh 데이터로 갱신
                self.current_settings = data

                coins_data = data.get(selected_strategy, {}).get("coins", [])

                # 기존 코인 블럭들 다 지우고
                for frame in self.coin_frames:
                    frame.destroy()
                self.coin_frames.clear()
                self.coin_entries.clear()
                 

                if coins_data:
                    for coin in coins_data:
                        self.add_coin_row(data=coin, strategy_type=selected_strategy)
                else:
                    self.add_coin_row(strategy_type=selected_strategy)
                self.update_strategy_info()
            else:
                messagebox.showerror("서버 오류", f"설정 불러오기 실패: {res.text}")

        except Exception as e:
            logging.exception("전략 변경 실패")
            messagebox.showerror("오류", f"전략 설정 불러오기 실패: {e}")
    

    
    def show_log_window(self):
        # ✅ 기존 로그 창이 있으면 포커스만 맞추기
        if self.log_window and self.log_window.winfo_exists():
            self.log_window.lift()  # 창을 맨 앞으로
            self.log_window.focus_force()  # 포커스 강제 설정
            return

        try:
            res = requests.get(f"{API_BASE_URL}/log/{self.username}", headers=API_HEADERS)
            if res.status_code == 200:
                log_content = res.json().get("log", "")
            else:
                messagebox.showerror("오류", f"로그 불러오기 실패: {res.text}")
                return
        except Exception as e:
            messagebox.showerror("오류", f"로그 요청 실패: {e}")
            return

        # 새 로그 창 생성
        self.log_window = tk.Toplevel(self.root)
        self.log_window.title(f"{self.username} - 로그 보기")
        self.log_window.geometry("800x600")
        self.log_window.configure(bg=self.theme_colors["bg"])

        # ✅ 창이 닫힐 때 참조 정리
        def on_log_window_close():
            if self.log_window:
                self.log_window.destroy()
                self.log_window = None

        self.log_window.protocol("WM_DELETE_WINDOW", on_log_window_close)

        # ✅ 메인 컨테이너 프레임
        main_frame = tk.Frame(self.log_window, bg=self.theme_colors["bg"])
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # ✅ 버튼 프레임을 먼저 배치 (하단 고정)
        button_frame = tk.Frame(main_frame, bg=self.theme_colors["bg"])
        button_frame.pack(side="bottom", fill="x", pady=(10, 0))

        # ✅ 텍스트 위젯과 스크롤바를 위한 프레임
        text_frame = tk.Frame(main_frame, bg=self.theme_colors["bg"])
        text_frame.pack(fill="both", expand=True)

        # ✅ 메인 테마에 맞는 텍스트 위젯
        text_widget = tk.Text(text_frame, wrap="none",
                             bg=self.theme_colors["input_bg"],
                             fg=self.theme_colors["text"],
                             font=("Consolas", 10),
                             insertbackground=self.theme_colors["text"],
                             selectbackground=self.theme_colors["accent"],
                             selectforeground=self.theme_colors["text_white"])
        text_widget.insert(tk.END, log_content)
        text_widget.config(state="disabled")
        text_widget.pack(side="left", fill="both", expand=True)

        # ✅ 메인 테마에 맞는 스크롤바 (우측 상단에 위치)
        scrollbar_y = tk.Scrollbar(text_frame, command=text_widget.yview,
                                  bg=self.theme_colors["panel_bg"],
                                  troughcolor=self.theme_colors["input_bg"],
                                  activebackground=self.theme_colors["accent"])
        # scrollbar_y.pack(side="right", fill="y")  # 스크롤바 숨김
        text_widget.config(yscrollcommand=scrollbar_y.set)

        # ✅ 로그 초기화 버튼
        def clear_log():
            confirm = messagebox.askyesno("확인", "정말 로그를 초기화하시겠습니까?")
            if not confirm:
                return
            try:
                res = requests.post(f"{API_BASE_URL}/clear_log", json={"username": self.username}, headers=API_HEADERS)
                if res.status_code == 200:
                    messagebox.showinfo("성공", "로그가 초기화되었습니다.")
                    text_widget.config(state="normal")
                    text_widget.delete("1.0", tk.END)
                    text_widget.insert(tk.END, "")
                    text_widget.config(state="disabled")
                else:
                    messagebox.showerror("실패", f"로그 초기화 실패: {res.text}")
            except Exception as e:
                messagebox.showerror("오류", f"로그 초기화 요청 실패: {e}")

        # 버튼 프레임은 이미 위에서 정의됨

        def refresh_log():
            try:
                res = requests.get(f"{API_BASE_URL}/log/{self.username}", headers=API_HEADERS)
                if res.status_code == 200:
                    new_log_content = res.json().get("log", "")
                    text_widget.config(state="normal")
                    text_widget.delete("1.0", tk.END)
                    text_widget.insert(tk.END, new_log_content)
                    text_widget.config(state="disabled")
                    # 스크롤을 맨 아래로
                    text_widget.see(tk.END)
                else:
                    messagebox.showerror("오류", f"로그 새로고침 실패: {res.text}")
            except Exception as e:
                messagebox.showerror("오류", f"로그 새로고침 요청 실패: {e}")

        # ✅ 메인 테마에 맞는 새로고침 버튼
        refresh_btn = tk.Button(button_frame, text="🔄 새로고침", command=refresh_log,
                               bg=self.theme_colors["button_bg"], fg=self.theme_colors["text"],
                               font=("Segoe UI", 10), relief="flat", padx=20, pady=5,
                               activebackground=self.theme_colors["button_hover"],
                               activeforeground=self.theme_colors["text"])
        refresh_btn.pack(side="left", padx=(0, 10))

        # ✅ 메인 테마에 맞는 초기화 버튼
        clear_btn = tk.Button(button_frame, text="🧹 로그 초기화", command=clear_log,
                             bg=self.theme_colors["danger"], fg=self.theme_colors["text_white"],
                             font=("Segoe UI", 10), relief="flat", padx=20, pady=5,
                             activebackground="#c82333", activeforeground=self.theme_colors["text_white"])
        clear_btn.pack(side="left")

    def build_modern_layout(self):
        """상하 분할 트레이딩 플랫폼 레이아웃"""
        # 상단 헤더
        self.create_header_bar()

        # 메인 컨테이너
        main_container = tk.Frame(self.root, bg=self.theme_colors["bg"])
        main_container.pack(fill="both", expand=True)

        # 전체를 좌우로 분할
        self.main_paned = tk.PanedWindow(main_container, orient="horizontal",
                                        bg=self.theme_colors["bg"], sashwidth=8,
                                        sashrelief="raised", sashpad=2)
        self.main_paned.pack(fill="both", expand=True, padx=5, pady=5)

        # 왼쪽 영역: 계정정보 + 코인설정 (상하 분할)
        left_paned = tk.PanedWindow(self.main_paned, orient="vertical",
                                   bg=self.theme_colors["bg"], sashwidth=6,
                                   sashrelief="raised", sashpad=1)

        # 계정 정보 패널 (상단)
        info_frame = tk.Frame(left_paned, bg=self.theme_colors["card_bg"])
        self.create_info_panel(info_frame)
        left_paned.add(info_frame, minsize=250)

        # 코인 설정 패널 (하단)
        coins_frame = tk.Frame(left_paned, bg=self.theme_colors["card_bg"])
        self.create_unified_coin_panel(coins_frame)
        left_paned.add(coins_frame, minsize=400)

        self.main_paned.add(left_paned, minsize=800)  # 600 -> 800으로 증가

        # 오른쪽 영역: 포지션 & 주문 현황
        position_frame = tk.Frame(self.main_paned, bg=self.theme_colors["card_bg"])
        self.create_position_panel(position_frame)
        self.main_paned.add(position_frame, minsize=600)  # 700 -> 600으로 감소

    def create_chart_panel(self, parent):
        """차트 표시 패널 - 상단 영역에 임베드"""
        # 헤더
        header = tk.Frame(parent, bg=self.theme_colors["panel_bg"], height=40)
        header.pack(fill="x")
        header.pack_propagate(False)

        tk.Label(header, text="실시간 차트", font=("Segoe UI", 12, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(side="left", padx=15, pady=10)

        # 차트 컨트롤
        controls = tk.Frame(header, bg=self.theme_colors["panel_bg"])
        controls.pack(side="right", padx=15, pady=5)

        self.current_symbol_label = tk.Label(controls, text="심볼 선택", font=("Segoe UI", 10),
                                           bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text_light"])
        self.current_symbol_label.pack(side="right", padx=10)

        # 차트 내용 영역
        self.chart_content = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        self.chart_content.pack(fill="both", expand=True, padx=10, pady=10)

        # 기본 메시지
        self.chart_placeholder = tk.Label(self.chart_content,
                                         text="포지션을 클릭하여 차트를 확인하세요",
                                         font=("Segoe UI", 14),
                                         bg=self.theme_colors["panel_bg"],
                                         fg=self.theme_colors["text_light"])
        self.chart_placeholder.pack(expand=True)

    def create_header_bar(self):
        """상단 헤더 바 - 트레이딩 플랫폼 스타일"""
        header = tk.Frame(self.root, bg=self.theme_colors["header_bg"], height=70)
        header.pack(fill="x")
        header.pack_propagate(False)

        # 좌측: 로고 및 사용자 정보
        left_header = tk.Frame(header, bg=self.theme_colors["header_bg"])
        left_header.pack(side="left", padx=20, pady=15)

        # 로고
        tk.Label(left_header, text="CoinPilot Pro", font=("Segoe UI", 18, "bold"),
                bg=self.theme_colors["header_bg"], fg=self.theme_colors["text_white"]).pack(side="left")
        tk.Label(left_header, text=f"| {self.username}", font=("Segoe UI", 12),
                bg=self.theme_colors["header_bg"], fg=self.theme_colors["text_light"]).pack(side="left", padx=(15, 0))

        # 중앙: 상태 표시
        center_header = tk.Frame(header, bg=self.theme_colors["header_bg"])
        center_header.pack(side="left", expand=True, padx=50)

        # 실행 상태 표시 - ✅ 밝은 초록색 사용
        status_text = "전략 실행 중" if self.is_running else "대기 중"
        status_color = self.theme_colors["success_bright"] if self.is_running else self.theme_colors["neutral"]
        self.status_label = tk.Label(center_header, text=status_text, font=("Segoe UI", 12, "bold"),
                                    bg=self.theme_colors["header_bg"], fg=status_color)
        self.status_label.pack()

        # 우측: 컨트롤 버튼들
        right_header = tk.Frame(header, bg=self.theme_colors["header_bg"])
        right_header.pack(side="right", padx=20, pady=15)

        # 기타 버튼들 (색상 통일)
        buttons = [
            ("비밀번호", self.change_password, self.theme_colors["secondary"]),
            ("로그", self.show_log_window, self.theme_colors["secondary"]),
            ("새로고침", self.load_settings, self.theme_colors["secondary"])
        ]

        for text, command, color in buttons:
            btn = tk.Button(right_header, text=text, font=("Segoe UI", 10),
                           bg=color, fg=self.theme_colors["text_white"],
                           relief="flat", padx=12, pady=6, command=command, cursor="hand2",
                           bd=0, highlightthickness=0)
            btn.pack(side="right", padx=3)

    def toggle_theme(self):
        """테마 전환 (라이트 <-> 다크)"""
        global CURRENT_THEME
        new_theme = "dark" if self.theme == "light" else "light"
        self.theme = new_theme
        CURRENT_THEME = new_theme
        self.theme_colors = THEME_COLORS[self.theme]

        # 전체 UI 새로고침
        self.refresh_ui()

    def refresh_ui(self):
        """UI 전체 새로고침"""
        # 기존 위젯들 제거
        for widget in self.root.winfo_children():
            widget.destroy()

        # UI 다시 생성
        self.root.configure(bg=self.theme_colors["bg"])
        self.build_modern_layout()

    def create_info_panel(self, parent):
        """계정 정보 패널"""
        # 헤더
        header = tk.Frame(parent, bg=self.theme_colors["panel_bg"], height=40)
        header.pack(fill="x")
        header.pack_propagate(False)

        tk.Label(header, text="Overview", font=("Segoe UI", 12, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(pady=10)

        # 컨트롤 버튼들을 위한 고정 공간 (하단에 먼저 배치)
        button_container = tk.Frame(parent, bg=self.theme_colors["panel_bg"], height=60)
        button_container.pack(side="bottom", fill="x", padx=10, pady=10)
        button_container.pack_propagate(False)  # 높이 고정

        # 버튼 프레임 (컨테이너 내부에 중앙 정렬)
        button_frame = tk.Frame(button_container, bg=self.theme_colors["panel_bg"])
        button_frame.pack(expand=True)

        # 전략 실행 버튼
        self.run_btn = tk.Button(button_frame, text="전략 실행", font=("Segoe UI", 11, "bold"),
                                bg=self.theme_colors["long"], fg=self.theme_colors["text_white"],
                                relief="flat", padx=20, pady=8, cursor="hand2", bd=0,
                                highlightthickness=0, command=self.run_strategy)
        self.run_btn.pack(side="left", padx=(0, 10))

        # 이익 후 중지 버튼 (노란색)
        self.stop_after_profit_btn = tk.Button(button_frame, text="이익 후 중지", font=("Segoe UI", 11, "bold"),
                                              bg="#FFD700", fg="#000000",  # 🔧 노란색 배경, 검은색 텍스트
                                              relief="flat", padx=20, pady=8, cursor="hand2", bd=0,
                                              highlightthickness=0, command=self.stop_after_profit)
        self.stop_after_profit_btn.pack(side="left", padx=(0, 10))

        # 즉시 중지 버튼
        self.stop_btn = tk.Button(button_frame, text="즉시 중지", font=("Segoe UI", 11, "bold"),
                                 bg=self.theme_colors["danger"], fg=self.theme_colors["text_white"],
                                 relief="flat", padx=20, pady=8, cursor="hand2", bd=0,
                                 highlightthickness=0, command=self.stop_strategy)
        self.stop_btn.pack(side="left")

        # 정보 텍스트 영역 (나머지 공간 사용)
        text_container = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        text_container.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        self.strategy_info_text = tk.Text(text_container, wrap="none",
                                         bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                                         font=("Consolas", 10), relief="flat", borderwidth=0)
        self.strategy_info_text.pack(side="left", fill="both", expand=True)

        # 가운데 정렬 태그 설정
        self.strategy_info_text.tag_configure("center", justify="center")

        self.text_scrollbar = tk.Scrollbar(text_container, orient="vertical",
                                          command=self.strategy_info_text.yview,
                                          bg=self.theme_colors["panel_bg"],
                                          troughcolor=self.theme_colors["panel_bg"],
                                          activebackground=self.theme_colors["text_light"],
                                          width=12)
        # self.text_scrollbar.pack(side="right", fill="y")  # 스크롤바 숨김
        self.strategy_info_text.configure(yscrollcommand=self.text_scrollbar.set)

        # 초기 버튼 상태 설정
        self.update_control_buttons()

    def create_unified_coin_panel(self, parent):
        """통합된 코인 설정 패널 (전략 선택 포함)"""
        # 헤더
        header = tk.Frame(parent, bg=self.theme_colors["panel_bg"], height=40)
        header.pack(fill="x")
        header.pack_propagate(False)

        header_content = tk.Frame(header, bg=self.theme_colors["panel_bg"])
        header_content.pack(expand=True, fill="both")

        tk.Label(header_content, text="코인 설정", font=("Segoe UI", 12, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(side="left", padx=15, pady=10)

        # 설정 저장 버튼 (헤더 우측)
        self.save_btn = tk.Button(header_content, text="설정 저장", font=("Segoe UI", 10, "bold"),
                                 bg=self.theme_colors["primary"], fg=self.theme_colors["text_dark"],
                                 relief="flat", padx=15, pady=5, cursor="hand2", bd=0,
                                 highlightthickness=0, command=self.save_settings)
        self.save_btn.pack(side="right", padx=(5, 15), pady=5)

        # 코인 추가 버튼
        add_btn = tk.Button(header_content, text="코인 추가", font=("Segoe UI", 10),
                           bg=self.theme_colors["secondary"], fg=self.theme_colors["text_white"],
                           relief="flat", padx=15, pady=5, cursor="hand2", bd=0,
                           highlightthickness=0,
                           command=lambda: self.add_coin_row(strategy_type="grid" if self.strategy_type_var.get() == "거미줄 매매법" else "cycle"))
        add_btn.pack(side="right", padx=5, pady=5)

        # 전략 선택 영역
        strategy_frame = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        strategy_frame.pack(fill="x", padx=15, pady=(10, 0))

        tk.Label(strategy_frame, text="전략 유형", font=("Segoe UI", 11, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(anchor="w", pady=(0, 5))

        self.strategy_type_var = tk.StringVar()

        # ttk.Combobox 스타일 설정 - 회색으로 변경
        style = ttk.Style()
        style.configure("Custom.TCombobox",
                       fieldbackground="#6C757D",  # 회색 배경
                       background="#6C757D",       # 회색 배경
                       foreground="#FFFFFF",       # 흰색 텍스트
                       borderwidth=1,
                       relief="solid")

        # 드롭다운 리스트 스타일도 회색으로 설정
        style.map("Custom.TCombobox",
                 fieldbackground=[('readonly', '#6C757D')],
                 selectbackground=[('readonly', '#5A6268')],
                 selectforeground=[('readonly', '#FFFFFF')])

        self.strategy_type_dropdown = ttk.Combobox(
            strategy_frame, textvariable=self.strategy_type_var,
            values=["거미줄 매매법", "순환매수매매 기법"], state="readonly", width=25,
            font=("Segoe UI", 11), style="Custom.TCombobox"
        )
        self.strategy_type_dropdown.set("거미줄 매매법")
        self.strategy_type_dropdown.pack(fill="x", pady=(0, 15))
        self.strategy_type_dropdown.bind("<<ComboboxSelected>>", self.on_strategy_type_change)

        # 스크롤 가능한 코인 설정 영역
        canvas_frame = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        canvas_frame.pack(fill="both", expand=True)

        self.canvas = tk.Canvas(canvas_frame, bg=self.theme_colors["panel_bg"], highlightthickness=0)

        # 다크모드에서 스크롤바 스타일 개선
        if self.theme_colors["bg"] == "#0d1117":  # 다크모드
            self.v_scroll = tk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview,
                                       bg=self.theme_colors["panel_bg"], troughcolor=self.theme_colors["panel_bg"],
                                       activebackground=self.theme_colors["text_light"], width=12)
        else:
            self.v_scroll = tk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)

        self.canvas.configure(yscrollcommand=self.v_scroll.set)
        self.coins_frame = tk.Frame(self.canvas, bg=self.theme_colors["panel_bg"])
        self.canvas_window = self.canvas.create_window((0, 0), window=self.coins_frame, anchor="nw")

        self.canvas.pack(side="left", fill="both", expand=True)
        # self.v_scroll.pack(side="right", fill="y")  # 스크롤바 숨김
        self.canvas.bind("<Configure>", lambda e: self.canvas.itemconfig(self.canvas_window, width=e.width))
        self.coins_frame.bind("<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))

        # 마우스가 코인 설정 영역에 있을 때만 스크롤 작동
        def on_enter_canvas(event):
            self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)

        def on_leave_canvas(event):
            self.canvas.unbind_all("<MouseWheel>")

        self.canvas.bind("<Enter>", on_enter_canvas)
        self.canvas.bind("<Leave>", on_leave_canvas)

    def create_position_panel(self, parent):
        """포지션 모니터링 패널 - 탭으로 분리"""
        # 헤더 - 텍스트 제거
        header = tk.Frame(parent, bg=self.theme_colors["panel_bg"], height=20)
        header.pack(fill="x")
        header.pack_propagate(False)

        # 탭 프레임 생성
        tab_frame = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        tab_frame.pack(fill="x", padx=10, pady=(0, 10))

        # 탭 버튼들
        self.position_tab_btn = tk.Button(tab_frame, text="Position",
                                         font=("Segoe UI", 11, "bold"),
                                         bg=self.theme_colors["primary"], fg="#000000",
                                         relief="flat", padx=20, pady=8,
                                         command=lambda: self.switch_position_tab("position"))
        self.position_tab_btn.pack(side="left", padx=(0, 2))

        self.orders_tab_btn = tk.Button(tab_frame, text="Orders",
                                       font=("Segoe UI", 11, "bold"),
                                       bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                                       relief="flat", padx=20, pady=8,
                                       command=lambda: self.switch_position_tab("orders"))
        self.orders_tab_btn.pack(side="left")

        # 탭 컨텐츠 프레임
        self.tab_content_frame = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        self.tab_content_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Position 탭 내용
        self.position_content = tk.Frame(self.tab_content_frame, bg=self.theme_colors["panel_bg"])
        self.create_position_table(self.position_content)

        # Orders 탭 내용
        self.orders_content = tk.Frame(self.tab_content_frame, bg=self.theme_colors["panel_bg"])
        self.create_orders_table(self.orders_content)

        # 현재 활성 탭
        self.current_position_tab = "position"
        self.switch_position_tab("position")

    def switch_position_tab(self, tab_name):
        """포지션 탭 전환"""
        # 모든 탭 숨기기
        self.position_content.pack_forget()
        self.orders_content.pack_forget()

        # 버튼 스타일 초기화
        self.position_tab_btn.config(bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"])
        self.orders_tab_btn.config(bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"])

        # 선택된 탭 표시
        if tab_name == "position":
            self.position_content.pack(fill="both", expand=True)
            self.position_tab_btn.config(bg=self.theme_colors["primary"], fg="#000000")
            # Position 탭으로 전환 시 포지션 데이터 강제 업데이트
            self.root.after(100, self.update_strategy_info)
        elif tab_name == "orders":
            self.orders_content.pack(fill="both", expand=True)
            self.orders_tab_btn.config(bg=self.theme_colors["primary"], fg="#000000")
            # 🔧 Orders 탭으로 전환 시 Position 탭 업데이트 중지하고 Orders만 업데이트
            self.root.after(100, self.update_orders_display)

        self.current_position_tab = tab_name

    def create_position_table(self, parent):
        """Position 탭의 테이블 생성"""

        # 테이블 스타일 설정
        style = ttk.Style()
        style.theme_use("clam")

        # 다크/라이트 모드에 따른 스타일 설정
        if self.theme == "dark":
            style.configure("Treeview",
                           background=self.theme_colors["panel_bg"],
                           foreground=self.theme_colors["text"],
                           fieldbackground=self.theme_colors["panel_bg"],
                           borderwidth=0)
            style.configure("Treeview.Heading",
                           background=self.theme_colors["header_bg"],
                           foreground=self.theme_colors["text"],
                           borderwidth=1,
                           relief="solid")
        else:
            style.configure("Treeview",
                           background="#ffffff",
                           foreground=self.theme_colors["text"],
                           fieldbackground="#ffffff",
                           borderwidth=0)
            style.configure("Treeview.Heading",
                           background=self.theme_colors["header_bg"],
                           foreground=self.theme_colors["text_white"],
                           borderwidth=1,
                           relief="solid")

        # 테이블 프레임
        table_frame = tk.Frame(parent, bg=self.theme_colors["panel_bg"])
        table_frame.pack(fill="both", expand=True)

        # 포지션 테이블 설명 라벨
        info_label = tk.Label(table_frame,
                             text="※ 실제 포지션이 있는 코인만 표시됩니다 (설정만 된 코인은 표시되지 않음)",
                             font=("Segoe UI", 9),
                             bg=self.theme_colors["panel_bg"],
                             fg=self.theme_colors["text_light"],
                             anchor="w")
        info_label.pack(fill="x", padx=5, pady=(0, 5))

        # 헤더 설정 (아이콘 제거)
        headers = ["심볼", "포지션", "수량", "진입가", "현재가", "손익", "수익률", "주문", "오류", "종료"]

        # CustomTable 사용 - CoinPilotApp 인스턴스 전달
        self.position_table = CustomTable(table_frame, headers, self.theme_colors,
                                         app_instance=self, bg=self.theme_colors["panel_bg"])
        self.position_table.pack(fill="both", expand=True)

        # CustomTable은 자체적으로 스크롤바와 색상을 관리함

        # 초기 너비 계산을 위한 다단계 지연 실행
        def set_initial_widths():
            try:
                self.position_table.update_column_widths()
                # 첫 번째 시도가 실패하면 다시 시도
                if not hasattr(self.position_table, 'actual_widths') or not self.position_table.actual_widths:
                    self.root.after(200, set_initial_widths)
            except:
                # 오류 발생 시 다시 시도
                self.root.after(200, set_initial_widths)

        # 여러 시점에서 너비 설정 시도
        self.root.after(50, set_initial_widths)   # 첫 번째 시도
        self.root.after(200, set_initial_widths)  # 두 번째 시도
        self.root.after(500, set_initial_widths)  # 세 번째 시도 (확실하게)

        # 클릭 이벤트 바인딩
        self.position_table.bind("<Button-1>", self.on_position_click)

    def create_orders_table(self, parent):
        """Orders 탭의 테이블 생성"""
        # 설명 라벨 제거

        # 스크롤 가능한 프레임
        canvas = tk.Canvas(parent, bg=self.theme_colors["panel_bg"], highlightthickness=0)
        scrollbar_orders = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        self.orders_scrollable_frame = tk.Frame(canvas, bg=self.theme_colors["panel_bg"])

        self.orders_scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.orders_scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar_orders.set)

        canvas.pack(side="left", fill="both", expand=True)
        # scrollbar_orders.pack(side="right", fill="y")  # 스크롤바 숨김

        # 마우스 휠 스크롤
        canvas.bind_all("<MouseWheel>", lambda e: canvas.yview_scroll(int(-1*(e.delta/120)), "units"))

        self.orders_canvas = canvas

        # 초기 메시지
        initial_label = tk.Label(self.orders_scrollable_frame,
                               text="주문 현황 데이터를 로딩 중...",
                               font=("Segoe UI", 11),
                               bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text_light"])
        initial_label.pack(pady=50)

    def update_orders_display(self):
        """Orders 탭 내용 업데이트"""
        try:
            # 기존 내용 제거
            for widget in self.orders_scrollable_frame.winfo_children():
                widget.destroy()

            # 🔧 strategy_info 캐시 갱신
            self._cached_strategy_info = None

            # 🔧 Orders 탭용 데이터 가져오기 (포지션 분석이 아닌 실제 주문 데이터)
            orders_data = self.get_orders_data()

            if not orders_data:
                no_data_label = tk.Label(self.orders_scrollable_frame,
                                       text="주문 현황 데이터가 없습니다.",
                                       font=("Segoe UI", 11),
                                       bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text_light"])
                no_data_label.pack(pady=50)
                return

            # 🔧 각 심볼별로 간단한 주문 현황 표시 (Position 탭과 분리)
            for symbol, data in orders_data.items():
                self.create_simple_orders_panel(symbol, data)

        except Exception as e:
            print(f"Orders 탭 업데이트 실패: {e}")

    def get_trade_analysis_data(self):
        """서버에서 포지션 분석 데이터 가져오기 (websocket_manager에서 생성)"""
        try:
            # 서버에서 포지션 분석 데이터 요청
            res = requests.get(f"{API_BASE_URL}/position_analysis/{self.username}", headers=API_HEADERS)
            if res.status_code == 200:
                return res.json().get("analysis_data", {})
            else:
                print(f"포지션 분석 데이터 요청 실패: {res.status_code}")
                return {}
        except Exception as e:
            print(f"포지션 분석 데이터 가져오기 실패: {e}")
            return {}

    def get_actual_strategy_type(self):
        """서버에서 실제 전략 타입 가져오기"""
        try:
            res = requests.get(f"{API_BASE_URL}/settings/{self.username}", headers=API_HEADERS)
            if res.status_code == 200:
                settings = res.json()
                return settings.get("strategy_type", "grid")
        except:
            pass
        return "grid"  # 기본값

    def get_orders_data(self):
        """Orders 탭용 주문 데이터 가져오기"""
        try:
            # strategy_info에서 실제 주문 데이터 가져오기
            res = requests.get(f"{API_BASE_URL}/strategy_info/{self.username}", headers=API_HEADERS)
            if res.status_code == 200:
                strategy_data = res.json()

                # 포지션이 있는 심볼들 찾기
                positions = strategy_data.get("positions", {})
                orders = strategy_data.get("orders", [])

                # 심볼별로 데이터 구성
                orders_data = {}

                # 포지션이 있는 심볼들
                for symbol, pos_data in positions.items():
                    if float(pos_data.get("positionAmt", 0)) != 0:
                        orders_data[symbol] = {
                            "symbol": symbol,
                            "position_side": "LONG" if float(pos_data.get("positionAmt", 0)) > 0 else "SHORT",
                            "entry_price": float(pos_data.get("entryPrice", 0)),
                            "realized_pnl": 0,  # 포지션 분석 데이터에서 가져와야 함
                            "additional_orders": [],  # 포지션 분석 데이터에서 가져와야 함
                            "profit_orders": []  # 포지션 분석 데이터에서 가져와야 함
                        }

                # 주문이 있는 심볼들도 추가
                for order in orders:
                    symbol = order.get("symbol")
                    if symbol and symbol not in orders_data:
                        orders_data[symbol] = {
                            "symbol": symbol,
                            "position_side": "N/A",
                            "entry_price": 0,
                            "realized_pnl": 0,
                            "additional_orders": [],
                            "profit_orders": []
                        }

                return orders_data
            else:
                print(f"Orders 데이터 가져오기 실패: {res.status_code}")
                return {}
        except Exception as e:
            print(f"Orders 데이터 가져오기 실패: {e}")
            return {}

    def create_simple_orders_panel(self, symbol, data):
        """Orders 탭 전용 간단한 주문 현황 패널"""
        # 심볼 프레임
        symbol_frame = tk.Frame(self.orders_scrollable_frame,
                               bg=self.theme_colors["input_bg"],
                               relief="solid", borderwidth=1)
        symbol_frame.pack(fill="x", padx=10, pady=5)

        # 심볼 헤더
        header_frame = tk.Frame(symbol_frame, bg=self.theme_colors["input_bg"])
        header_frame.pack(fill="x", padx=10, pady=5)

        symbol_label = tk.Label(header_frame, text=f"📊 {symbol}",
                               font=("Segoe UI", 12, "bold"),
                               bg=self.theme_colors["input_bg"], fg=self.theme_colors["primary"])
        symbol_label.pack(side="left")

        # 진입가 표시
        entry_price = data.get('entry_price', 0)
        if entry_price > 0:
            entry_label = tk.Label(header_frame, text=f"진입가: {entry_price:.4f}",
                                  font=("Segoe UI", 10),
                                  bg=self.theme_colors["input_bg"], fg=self.theme_colors["text"])
            entry_label.pack(side="right", padx=(0, 10))

        # 🔧 상세 주문 현황 테이블 생성
        self.create_detailed_orders_table(symbol_frame, symbol, data)

    def create_detailed_orders_table(self, parent, symbol, data):
        """가격대별 상세 주문 현황 테이블"""
        # 현재 지정가 주문 정보
        current_orders = self.get_limit_orders_from_strategy_info(symbol)

        # 체결된 거래 정보 (포지션 분석에서 가져와야 함)
        additional_orders = data.get('additional_orders', [])
        profit_orders = data.get('profit_orders', [])

        # 모든 가격대 수집
        all_prices = set()

        # 현재 지정가 주문 가격
        for order in current_orders:
            price = float(order.get('price', 0))
            if price > 0:
                all_prices.add(price)

        # 체결된 거래 가격
        for order in additional_orders + profit_orders:
            price = float(order.get('price', 0))
            if price > 0:
                all_prices.add(price)

        if not all_prices:
            no_data_frame = tk.Frame(parent, bg=self.theme_colors["input_bg"])
            no_data_frame.pack(fill="x", padx=10, pady=5)
            tk.Label(no_data_frame, text="📭 주문 데이터 없음",
                    font=("Segoe UI", 10),
                    bg=self.theme_colors["input_bg"], fg=self.theme_colors["text_light"]).pack(pady=10)
            return

        # 가격대별로 정렬 (높은 가격부터)
        sorted_prices = sorted(all_prices, reverse=True)

        # 테이블 프레임
        table_frame = tk.Frame(parent, bg=self.theme_colors["input_bg"])
        table_frame.pack(fill="x", padx=10, pady=5)

        # 헤더
        header_frame = tk.Frame(table_frame, bg=self.theme_colors["header_bg"])
        header_frame.pack(fill="x", pady=(0, 2))

        # ✅ 실제 서버에서 받아온 전략 타입 확인
        actual_strategy_type = self.get_actual_strategy_type()

        # ✅ 거미줄 매매일 때는 부분매도 컬럼 제거
        if actual_strategy_type == "grid":
            headers = ["가격", "지정가주문", "체결상태"]
            widths = [12, 12, 12]  # 거미줄 매매: 넓은 간격
            header_padx = 2  # 거미줄 매매: 넓은 패딩
        else:
            headers = ["가격", "지정가주문", "체결상태", "부분매도"]
            widths = [12, 12, 12, 15]  # 순환매매: 좁은 간격
            header_padx = 1  # 순환매매: 좁은 패딩

        for i, (header, width) in enumerate(zip(headers, widths)):
            tk.Label(header_frame, text=header, font=("Segoe UI", 9, "bold"),
                    bg=self.theme_colors["header_bg"], fg=self.theme_colors["text_white"],
                    width=width, anchor="center").pack(side="left", padx=header_padx)

        # 각 가격대별 행 생성
        for price in sorted_prices:  # 최대 20개만 표시
            self.create_price_row(table_frame, price, current_orders, additional_orders, profit_orders)

    def create_price_row(self, parent, price, current_orders, additional_orders, profit_orders):
        """가격대별 행 생성"""
        row_frame = tk.Frame(parent, bg=self.theme_colors["input_bg"])
        row_frame.pack(fill="x", pady=1)

        # ✅ 실제 전략 타입 사용
        actual_strategy_type = self.get_actual_strategy_type()

        # ✅ 거미줄 매매일 때와 순환매매일 때 컬럼 너비와 간격 구분
        if actual_strategy_type == "grid":
            col_width = 12  # 거미줄 매매: 넓은 컬럼
            data_padx = 2   # 거미줄 매매: 넓은 패딩
        else:
            col_width = 12  # 순환매매: 좁은 컬럼
            data_padx = 1   # 순환매매: 좁은 패딩

        # 가격
        tk.Label(row_frame, text=f"{price:.4f}", font=("Consolas", 9),
                bg=self.theme_colors["input_bg"], fg=self.theme_colors["text"],
                width=col_width, anchor="center").pack(side="left", padx=data_padx)

        # 지정가 주문
        limit_qty = 0
        for order in current_orders:
            if abs(float(order.get('price', 0)) - price) < 0.0001:
                limit_qty += float(order.get('origQty', 0))

        limit_text = f"{limit_qty:.1f}" if limit_qty > 0 else "-"
        tk.Label(row_frame, text=limit_text, font=("Consolas", 9),
                bg=self.theme_colors["input_bg"], fg=self.theme_colors["primary"] if limit_qty > 0 else self.theme_colors["text_light"],
                width=col_width, anchor="center").pack(side="left", padx=data_padx)

        # 체결 상태
        filled_qty = 0
        for order in additional_orders:
            if abs(float(order.get('price', 0)) - price) < 0.0001:
                filled_qty += float(order.get('qty', 0))

        filled_text = f"✓ {filled_qty:.1f}" if filled_qty > 0 else "-"
        tk.Label(row_frame, text=filled_text, font=("Consolas", 9),
                bg=self.theme_colors["input_bg"], fg=self.theme_colors["success"] if filled_qty > 0 else self.theme_colors["text_light"],
                width=col_width, anchor="center").pack(side="left", padx=data_padx)

        # ✅ 거미줄 매매일 때는 부분매도 컬럼 제거
        if actual_strategy_type != "grid":
            # 부분매도 (순환매매에서만 표시)
            profit_qty = 0
            profit_pnl = 0
            for order in profit_orders:
                if abs(float(order.get('price', 0)) - price) < 0.0001:
                    profit_qty += float(order.get('qty', 0))
                    profit_pnl += float(order.get('pnl', 0))

            if profit_qty > 0:
                profit_text = f"✓ {profit_qty:.1f} (+{profit_pnl:.2f})"
            else:
                profit_text = "-"

            # 순환매매에서 부분매도 컬럼 (동일한 패딩 적용)
            tk.Label(row_frame, text=profit_text, font=("Consolas", 9),
                    bg=self.theme_colors["input_bg"], fg=self.theme_colors["success"] if profit_qty > 0 else self.theme_colors["text_light"],
                    width=15, anchor="center").pack(side="left", padx=data_padx)

    def create_symbol_orders_panel(self, symbol, data):
        """심볼별 주문 현황 패널 생성 - 개선된 버전"""
        # 심볼 프레임
        symbol_frame = tk.Frame(self.orders_scrollable_frame,
                               bg=self.theme_colors["input_bg"],
                               relief="solid", borderwidth=1)
        symbol_frame.pack(fill="x", padx=10, pady=5)

        # 심볼 헤더
        header_frame = tk.Frame(symbol_frame, bg=self.theme_colors["input_bg"])
        header_frame.pack(fill="x", padx=10, pady=5)

        symbol_label = tk.Label(header_frame, text=f"📊 {symbol} ({data.get('position_side', 'N/A')})",
                               font=("Segoe UI", 12, "bold"),
                               bg=self.theme_colors["input_bg"], fg=self.theme_colors["primary"])
        symbol_label.pack(side="left")

        # 진입가와 실현손익 표시
        entry_price = data.get('entry_price', 0)
        realized_pnl = data.get('realized_pnl', 0)

        info_frame = tk.Frame(header_frame, bg=self.theme_colors["input_bg"])
        info_frame.pack(side="right")

        if entry_price > 0:
            entry_label = tk.Label(info_frame, text=f"진입가: {entry_price:.4f}",
                                  font=("Segoe UI", 10),
                                  bg=self.theme_colors["input_bg"], fg=self.theme_colors["text"])
            entry_label.pack(side="right", padx=(0, 10))

        if realized_pnl != 0:
            pnl_color = self.theme_colors["success"] if realized_pnl >= 0 else self.theme_colors["loss"]
            pnl_label = tk.Label(info_frame, text=f"실현손익: {realized_pnl:+.2f} USDT",
                                font=("Segoe UI", 10, "bold"),
                                bg=self.theme_colors["input_bg"], fg=pnl_color)
            pnl_label.pack(side="right", padx=(0, 10))

        # 🆕 통합 주문 현황 테이블 생성
        self.create_orders_status_table(symbol_frame, data)

        # 🆕 간단한 요약 정보 추가
        self.create_orders_summary(symbol_frame, data)

    def create_orders_status_table(self, parent, data):
        """통합 주문 현황 테이블 생성"""
        # 🔧 올바른 방식: 기존 strategy_info에서 지정가 주문 정보 가져오기
        symbol = data.get('symbol', '')
        current_orders = self.get_limit_orders_from_strategy_info(symbol)

        # 체결된 거래 정보
        additional_orders = data.get('additional_orders', [])
        profit_orders = data.get('profit_orders', [])

        # 로그 제거됨

        # 모든 가격대 수집 및 정렬
        all_prices = set()

        # 현재 지정가 주문 가격
        for order in current_orders:
            price = float(order.get('price', 0))
            if price > 0:
                all_prices.add(price)

        # 체결된 거래 가격
        for order in additional_orders + profit_orders:
            price = float(order.get('price', 0))
            if price > 0:
                all_prices.add(price)

        print(f"  수집된 가격대: {len(all_prices)}개")

        # 데이터가 없으면 체결된 거래만으로라도 테이블 생성
        if not all_prices:
            # 체결된 거래가 있으면 그것만으로라도 표시
            if additional_orders or profit_orders:
                for order in additional_orders + profit_orders:
                    price = float(order.get('price', 0))
                    if price > 0:
                        all_prices.add(price)

            # 정말 아무 데이터도 없으면 메시지 표시
            if not all_prices:
                no_data_frame = tk.Frame(parent, bg=self.theme_colors["input_bg"])
                no_data_frame.pack(fill="x", padx=10, pady=5)

                tk.Label(no_data_frame, text="📊 주문 현황 데이터 없음 (지정가 주문 및 체결 내역 없음)",
                        font=("Segoe UI", 10),
                        bg=self.theme_colors["input_bg"], fg=self.theme_colors["text_light"]).pack(pady=10)
                return

        # 가격 정렬 (높은 가격부터)
        sorted_prices = sorted(all_prices, reverse=True)

        # 테이블 프레임
        table_frame = tk.Frame(parent, bg=self.theme_colors["input_bg"])
        table_frame.pack(fill="x", padx=10, pady=5)

        # 테이블 헤더
        header_frame = tk.Frame(table_frame, bg=self.theme_colors["panel_bg"])
        header_frame.pack(fill="x", pady=(0, 2))

        tk.Label(header_frame, text="가격", font=("Segoe UI", 10, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                width=12, anchor="center").pack(side="left", padx=2)

        tk.Label(header_frame, text="지정가주문", font=("Segoe UI", 10, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                width=12, anchor="center").pack(side="left", padx=2)

        tk.Label(header_frame, text="체결상태", font=("Segoe UI", 10, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                width=12, anchor="center").pack(side="left", padx=2)

        # ✅ 거미줄 매매일 때는 부분매도 헤더 제거
        if self.strategy_type != "grid":
            tk.Label(header_frame, text="부분매도", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                    width=12, anchor="center").pack(side="left", padx=2)

        # 각 가격대별 상태 표시
        for price in sorted_prices:
            self.create_price_row(table_frame, price, current_orders, additional_orders, profit_orders)

    def create_price_row(self, parent, price, current_orders, additional_orders, profit_orders):
        """가격대별 주문 상태 행 생성"""
        row_frame = tk.Frame(parent, bg=self.theme_colors["input_bg"])
        row_frame.pack(fill="x", pady=1)

        # 가격 표시
        tk.Label(row_frame, text=f"{price:.4f}", font=("Consolas", 9),
                bg=self.theme_colors["input_bg"], fg=self.theme_colors["text"],
                width=12, anchor="center").pack(side="left", padx=2)

        # 지정가 주문 상태
        limit_order_info = self.get_limit_order_info(price, current_orders)
        limit_color = self.theme_colors["warning"] if limit_order_info["has_order"] else self.theme_colors["text_light"]
        tk.Label(row_frame, text=limit_order_info["text"], font=("Consolas", 9),
                bg=self.theme_colors["input_bg"], fg=limit_color,
                width=12, anchor="center").pack(side="left", padx=2)

        # 체결 상태
        filled_info = self.get_filled_info(price, additional_orders)
        filled_color = self.theme_colors["success"] if filled_info["is_filled"] else self.theme_colors["text_light"]
        tk.Label(row_frame, text=filled_info["text"], font=("Consolas", 9),
                bg=self.theme_colors["input_bg"], fg=filled_color,
                width=12, anchor="center").pack(side="left", padx=2)

        # ✅ 거미줄 매매일 때는 부분매도 상태 제거
        if self.strategy_type != "grid":
            # 부분매도 상태 (순환매매에서만 표시)
            profit_info = self.get_profit_info(price, profit_orders)
            profit_color = self.theme_colors["success"] if profit_info["has_profit"] else self.theme_colors["text_light"]
            tk.Label(row_frame, text=profit_info["text"], font=("Consolas", 9),
                    bg=self.theme_colors["input_bg"], fg=profit_color,
                    width=12, anchor="center").pack(side="left", padx=2)

    def get_limit_orders_from_strategy_info(self, symbol):
        """기존 strategy_info에서 지정가 주문 정보 조회"""
        try:
            # 🔧 올바른 방식: 이미 가져온 strategy_info 데이터 활용
            if hasattr(self, '_cached_strategy_info') and self._cached_strategy_info:
                # 새로운 형태의 open_limit_orders 확인
                open_orders = self._cached_strategy_info.get("open_limit_orders", {})

                if isinstance(open_orders, dict) and symbol in open_orders:
                    result = open_orders[symbol]
                    if isinstance(result, list):
                        return result

                # 🔧 fallback: 기존 orders 배열에서 해당 심볼 필터링
                all_orders = self._cached_strategy_info.get("orders", [])
                symbol_orders = [order for order in all_orders if order.get("symbol") == symbol]
                return symbol_orders

            # 캐시가 없으면 새로 조회 - 새로 만든 strategy_info API 사용
            res = requests.get(f"{API_BASE_URL}/strategy_info/{self.username}", headers=API_HEADERS)

            if res.status_code == 200:
                strategy_data = res.json()
                self._cached_strategy_info = strategy_data  # 캐시 저장

                # 새로운 형태의 open_limit_orders 확인
                open_orders = strategy_data.get("open_limit_orders", {})

                if isinstance(open_orders, dict) and symbol in open_orders:
                    result = open_orders[symbol]

                    if isinstance(result, list):
                        return result

                # 🔧 fallback: 기존 orders 배열에서 해당 심볼 필터링
                all_orders = strategy_data.get("orders", [])
                symbol_orders = [order for order in all_orders if order.get("symbol") == symbol]
                return symbol_orders
            return []
        except Exception as e:
            return []

    def get_limit_order_info(self, price, current_orders):
        """지정가 주문 정보 반환"""
        for order in current_orders:
            if abs(float(order.get('price', 0)) - price) < 0.0001:
                qty = float(order.get('origQty', 0))
                return {"has_order": True, "text": f"{qty:.4f}"}
        return {"has_order": False, "text": "-"}

    def get_filled_info(self, price, additional_orders):
        """체결 정보 반환"""
        for order in additional_orders:
            if abs(float(order.get('price', 0)) - price) < 0.0001:
                qty = float(order.get('qty', 0))
                return {"is_filled": True, "text": f"✓ {qty:.4f}"}
        return {"is_filled": False, "text": "-"}

    def get_profit_info(self, price, profit_orders):
        """부분매도 정보 반환"""
        for order in profit_orders:
            if abs(float(order.get('price', 0)) - price) < 0.0001:
                qty = float(order.get('qty', 0))
                pnl = float(order.get('pnl', 0))
                return {"has_profit": True, "text": f"✓ {qty:.4f} (+{pnl:.2f})"}
        return {"has_profit": False, "text": "-"}

    def create_orders_summary(self, parent, data):
        """주문 현황 요약 정보 표시"""
        summary_frame = tk.Frame(parent, bg=self.theme_colors["input_bg"])
        summary_frame.pack(fill="x", padx=10, pady=5)

        # 제목
        tk.Label(summary_frame, text="📊 주문 현황 요약",
                font=("Segoe UI", 11, "bold"),
                bg=self.theme_colors["input_bg"], fg=self.theme_colors["primary"]).pack(anchor="w", pady=(0, 5))

        # 데이터 수집
        additional_orders = data.get('additional_orders', [])
        profit_orders = data.get('profit_orders', [])
        manual_trades = data.get('manual_trades', [])
        realized_pnl = data.get('realized_pnl', 0)
        total_commission = data.get('total_commission', 0)

        # 요약 정보 표시
        info_lines = []

        if additional_orders:
            info_lines.append(f"🔵 추가매수: {len(additional_orders)}건")
            for order in additional_orders[:3]:  # 최대 3개만 표시
                price = order.get('price', 0)
                qty = order.get('qty', 0)
                info_lines.append(f"   └ {price:.4f} × {qty:.4f}")

        if profit_orders:
            info_lines.append(f"🟢 부분익절: {len(profit_orders)}건")
            for order in profit_orders[:3]:  # 최대 3개만 표시
                price = order.get('price', 0)
                qty = order.get('qty', 0)
                pnl = order.get('pnl', 0)
                info_lines.append(f"   └ {price:.4f} × {qty:.4f} (+{pnl:.2f})")

        if manual_trades:
            info_lines.append(f"⚠️ 수동거래: {len(manual_trades)}건")

        if realized_pnl != 0:
            pnl_color = "🟢" if realized_pnl > 0 else "🔴"
            info_lines.append(f"{pnl_color} 실현손익: {realized_pnl:+.2f} USDT")

        if total_commission > 0:
            info_lines.append(f"💰 총 수수료: {total_commission:.4f} USDT")

        # 정보가 없으면 기본 메시지
        if not info_lines:
            info_lines.append("📭 거래 내역이 없습니다")

        # 각 라인 표시
        for line in info_lines:
            tk.Label(summary_frame, text=line,
                    font=("Segoe UI", 9),
                    bg=self.theme_colors["input_bg"], fg=self.theme_colors["text"],
                    anchor="w").pack(anchor="w", padx=10)

    def on_position_click(self, event):
        """포지션 테이블 클릭 이벤트 처리"""
        try:
            # 클릭된 항목 확인
            item = self.position_table.selection()[0] if self.position_table.selection() else None
            if not item:
                return

            # 클릭된 컬럼 확인
            column = self.position_table.identify_column(event.x)
            values = self.position_table.item(item)["values"]

            if not values or len(values) < 10:
                return

            symbol = values[0]  # 심볼

            # "종료" 컬럼 클릭 시 (마지막 컬럼)
            if column == "#10":  # 종료 버튼 컬럼
                self.close_single_position_by_symbol(symbol)
            else:
                # 다른 컬럼 클릭 시 차트 열기
                self.open_chart(symbol)

        except Exception as e:
            print(f"포지션 클릭 이벤트 처리 실패: {e}")

    def update_control_buttons(self):
        """실행 상태에 따른 버튼 표시/숨김"""
        if hasattr(self, 'run_btn') and hasattr(self, 'stop_btn') and hasattr(self, 'stop_after_profit_btn'):
            if self.is_running:
                self.run_btn.pack_forget()
                self.stop_btn.pack(side="left", padx=(0, 10))
                self.stop_after_profit_btn.pack(side="left", padx=(0, 10))
                if hasattr(self, 'status_label'):
                    self.status_label.config(text="전략 실행 중", fg=self.theme_colors["success_bright"])
            else:
                self.stop_btn.pack_forget()
                self.stop_after_profit_btn.pack_forget()
                self.run_btn.pack(side="left", padx=(0, 10))
                if hasattr(self, 'status_label'):
                    self.status_label.config(text="대기 중", fg=self.theme_colors["neutral"])



    def update_button_visibility(self, is_running: bool):
        if self.is_running == is_running:
            return  # 상태가 같으면 아무 것도 안 함

        self.is_running = is_running

        # 전략 실행 중이면 드롭다운 비활성화, 중지되면 활성화
        if hasattr(self, 'strategy_type_dropdown'):
            self.strategy_type_dropdown.config(state="readonly")

        # 버튼 상태 업데이트
        self.update_control_buttons()

    def _on_mousewheel(self, event):
        """ 마우스 휠 스크롤 처리 (스크롤 끝나면 멈춤) """
        if self.canvas.bbox("all") is None:
            return
        yview = self.canvas.yview()
        if (event.delta > 0 and yview[0] <= 0.0) or (event.delta < 0 and yview[1] >= 1.0):
            return
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def open_symbol_popup(self, target_entry):
        popup = tk.Toplevel(self.root)
        popup.title("심볼 선택")
        popup.geometry("400x500")

        try:
            client = Client()
            info = client.futures_exchange_info()
            symbols = []
            for s in info["symbols"]:
                if (
                    s.get("quoteAsset") == "USDT" and
                    s.get("contractType") == "PERPETUAL" and
                    s.get("status") == "TRADING"
                ):
                    symbols.append(s["symbol"])
        except Exception as e:
            messagebox.showerror("오류", f"바이낸스 심볼 불러오기 실패: {e}")
            popup.destroy()
            return

        search_var = tk.StringVar()

        def update_list(*args):
            query = search_var.get().upper()
            filtered = [s for s in symbols if query in s]
            listbox.delete(0, tk.END)
            for sym in filtered:
                listbox.insert(tk.END, sym)

        tk.Label(popup, text="심볼 검색").pack(pady=5)
        search_entry = tk.Entry(popup, textvariable=search_var)
        search_entry.pack(fill="x", padx=10)
        search_var.trace("w", update_list)

        listbox = tk.Listbox(popup)
        listbox.pack(fill="both", expand=True, padx=10, pady=10)

        def on_symbol_select(event):
            selection = listbox.curselection()
            if selection:
                sym = listbox.get(selection[0])

                # 🆕 현재 코인 블럭에 sym 이미 있는지 검사
                existing_symbols = set()
                for (symbol_entry, *_) in self.coin_entries:
                    existing_symbols.add(symbol_entry.get().strip().upper())

                if sym.upper() in existing_symbols:
                    messagebox.showerror("중복 심볼", f"{sym} 심볼이 이미 추가되어 있습니다.")
                    popup.destroy()
                    return

                target_entry.config(state="normal")
                target_entry.delete(0, tk.END)
                target_entry.insert(0, sym)
                target_entry.config(state="readonly")
                popup.destroy()


        listbox.bind("<Double-1>", on_symbol_select)

        update_list()

    def add_custom_block(self, parent_frame, level_value=None, multiplier_value=None, deletable=True):
        block_frame = tk.Frame(parent_frame, bg=self.theme_colors["hover"])
        block_frame.pack(fill="x", pady=2)

        level_entry = tk.Entry(block_frame, width=10, bg=self.theme_colors["input_bg"],
                              fg="white", insertbackground="white", font=("Segoe UI", 10))
        level_entry.pack(side="left", padx=5)
        if level_value is not None:
            level_entry.insert(0, str(level_value))

        multiplier_entry = tk.Entry(block_frame, width=10, bg=self.theme_colors["input_bg"],
                                   fg="white", insertbackground="white", font=("Segoe UI", 10))
        multiplier_entry.pack(side="left", padx=5)
        if multiplier_value is not None:
            multiplier_entry.insert(0, str(multiplier_value))

        # 수정: level=0, multiplier=1 이면 삭제 버튼을 강제로 없앤다
        if deletable and not (level_value == 0 and multiplier_value == 1):
            del_btn = tk.Button(block_frame, text="X", command=lambda: block_frame.destroy(),
                               bg=self.theme_colors["danger"], fg=self.theme_colors["text_white"],
                               font=("Segoe UI", 9), relief="flat", padx=8, pady=2)
            del_btn.pack(side="left", padx=5)

    def try_remove_coin_row(self, frame, is_saved):
        if self.is_running and is_saved:
            messagebox.showwarning("삭제 불가", "전략 중지 후에 삭제해 주십시오.")
            return
        self._remove_coin_row_by_frame(frame)

    def add_coin_row(self, data: dict = None, strategy_type: str = "grid", is_saved: bool = False):
        """현대적인 카드 스타일의 코인 설정 행 추가"""


        # 메인 카드 프레임
        card_frame = tk.Frame(
            self.coins_frame,
            bg=self.theme_colors["panel_bg"],
            relief="solid",
            borderwidth=1,
            highlightbackground=self.theme_colors["border"],
            highlightcolor=self.theme_colors["border"],
            highlightthickness=1,
            padx=20,
            pady=15
        )
        card_frame.pack(fill="x", pady=8, padx=10)  # 패딩 조정으로 더 많은 공간 확보

        # 카드 프레임을 포커스 가능하게 설정
        card_frame.config(takefocus=True)

        # 코인 설정 영역 클릭 시 포커스 및 스크롤 기능
        def on_card_click(event):
            try:
                # 포커스 설정
                card_frame.focus_set()

                # 레이아웃 업데이트
                self.root.update_idletasks()
                self.canvas.update_idletasks()
                self.coins_frame.update_idletasks()

                # 스크롤 영역 재계산
                self.canvas.configure(scrollregion=self.canvas.bbox("all"))

                # 카드 프레임의 실제 Y 위치 계산 (coins_frame 내에서의 위치)
                card_y = card_frame.winfo_y()

                # 캔버스 높이
                canvas_height = self.canvas.winfo_height()

                # 스크롤 영역 정보
                scroll_region = self.canvas.bbox("all")

                if scroll_region and canvas_height > 0:
                    total_height = scroll_region[3] - scroll_region[1]

                    if total_height > canvas_height:
                        # 카드가 화면 상단 1/4 지점에 오도록 계산
                        target_y = card_y - (canvas_height * 0.25)

                        # 스크롤 비율 계산 (0.0 ~ 1.0)
                        scroll_fraction = target_y / (total_height - canvas_height)
                        scroll_fraction = max(0.0, min(1.0, scroll_fraction))

                        # 스크롤 실행
                        self.canvas.yview_moveto(scroll_fraction)

                        print(f"스크롤 실행: card_y={card_y}, target_y={target_y}, fraction={scroll_fraction}")

            except Exception as e:
                print(f"스크롤 오류: {e}")

        # 카드 프레임과 모든 하위 위젯에 클릭 이벤트 바인딩 (Entry, Button 제외)
        def bind_click_recursive(widget):
            # Entry나 Button 같은 상호작용 위젯은 제외
            if not isinstance(widget, (tk.Entry, tk.Button, tk.Radiobutton, tk.Checkbutton)):
                widget.bind("<Button-1>", on_card_click)

            for child in widget.winfo_children():
                bind_click_recursive(child)

        bind_click_recursive(card_frame)

        # 카드 헤더 (배경색 투명화)
        header_frame = tk.Frame(card_frame, bg=self.theme_colors["panel_bg"], height=35)
        header_frame.pack(fill="x", pady=(0, 15))
        header_frame.pack_propagate(False)

        # 헤더 내용
        header_content = tk.Frame(header_frame, bg=self.theme_colors["panel_bg"])
        header_content.pack(expand=True, fill="both")

        strategy_name = "거미줄 매매법" if strategy_type == "grid" else "순환매수매매"

        tk.Label(header_content, text=f"{strategy_name} 설정",
                font=("Segoe UI", 11, "bold"),
                bg=self.theme_colors["panel_bg"],
                fg=self.theme_colors["text"]).pack(side="left", padx=10, pady=8)

        # 삭제 버튼
        del_btn = tk.Button(header_content, text="삭제", font=("Segoe UI", 9),
                           bg=self.theme_colors["danger"], fg=self.theme_colors["text_white"],
                           relief="flat", padx=10, pady=5, cursor="hand2", bd=0,
                           highlightthickness=0,
                           command=lambda: self.try_remove_coin_row(card_frame, is_saved=is_saved))
        del_btn.pack(side="right", padx=10, pady=3)

        # 메인 설정 영역
        main_settings = tk.Frame(card_frame, bg=self.theme_colors["panel_bg"])
        main_settings.pack(fill="x", pady=(0, 10))

        # 첫 번째 행: 심볼 선택
        symbol_row = tk.Frame(main_settings, bg=self.theme_colors["panel_bg"])
        symbol_row.pack(fill="x", pady=5)

        tk.Label(symbol_row, text="거래 심볼", font=("Segoe UI", 10, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))

        symbol_frame = tk.Frame(symbol_row, bg=self.theme_colors["panel_bg"])
        symbol_frame.pack(side="left")

        symbol_entry = tk.Entry(symbol_frame, width=15, font=("Segoe UI", 10),
                               state="readonly", relief="solid", borderwidth=1,
                               bg=self.theme_colors["input_bg"], fg=self.theme_colors["text_light"],
                               readonlybackground=self.theme_colors["input_bg"])
        symbol_entry.pack(side="left")

        def open_symbol_selector():
            self.open_symbol_popup(symbol_entry)

        browse_btn = tk.Button(symbol_frame, text="선택", font=("Segoe UI", 9),
                              bg=self.theme_colors["secondary"], fg=self.theme_colors["text"],
                              relief="flat", padx=10, pady=2, cursor="hand2", bd=0,
                              highlightthickness=0, command=open_symbol_selector)
        browse_btn.pack(side="left", padx=5)

        # 두 번째 행: 사용 비율
        fraction_row = tk.Frame(main_settings, bg=self.theme_colors["panel_bg"])
        fraction_row.pack(fill="x", pady=5)

        tk.Label(fraction_row, text="사용 비율 (%)", font=("Segoe UI", 10, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))

        fraction_entry = tk.Entry(fraction_row, width=15, font=("Segoe UI", 10),
                                 relief="solid", borderwidth=1, bg=self.theme_colors["input_bg"],
                                 fg="white", insertbackground="white")
        fraction_entry.pack(side="left")

        # 세 번째 행: 포지션 모드
        mode_row = tk.Frame(main_settings, bg=self.theme_colors["panel_bg"])
        mode_row.pack(fill="x", pady=5)

        tk.Label(mode_row, text="포지션 모드", font=("Segoe UI", 10, "bold"),
                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 15))  # 10 -> 15로 간격 증가

        mode_frame = tk.Frame(mode_row, bg=self.theme_colors["panel_bg"])
        mode_frame.pack(side="left")

        if strategy_type == "grid":
            mode_var = tk.StringVar(value="both")

            # 라디오 버튼 스타일 개선 - 롱/숏 색상 적용
            radio_configs = [
                ("롱만", "long", self.theme_colors["long"]),
                ("숏만", "short", self.theme_colors["short"]),
                ("둘다", "both", self.theme_colors["primary"]),
                ("조건부 진입", "conditional", self.theme_colors["info"])
            ]

            for text, value, color in radio_configs:
                # 커스텀 라디오 버튼 생성
                def create_custom_radio(text, value):
                    def on_select():
                        mode_var.set(value)
                        # 모든 라디오 버튼 색상 리셋
                        for widget in mode_frame.winfo_children():
                            if isinstance(widget, tk.Radiobutton):
                                widget.config(fg=self.theme_colors["text"])
                        # 선택된 버튼만 노란색으로 변경
                        rb.config(fg=self.theme_colors["accent"])

                    rb = tk.Radiobutton(mode_frame, text=text, variable=mode_var, value=value,
                                       bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                                       font=("Segoe UI", 10, "bold"), selectcolor="black",
                                       activeforeground=self.theme_colors["text"], activebackground=self.theme_colors["panel_bg"],
                                       indicatoron=1, relief="flat", borderwidth=0, command=on_select)
                    return rb

                rb = create_custom_radio(text, value)
                rb.pack(side="left", padx=(0, 15))

            # 초기 선택 상태 설정
            def update_radio_colors():
                current_value = mode_var.get()
                for widget in mode_frame.winfo_children():
                    if isinstance(widget, tk.Radiobutton):
                        if widget.cget("value") == current_value:
                            widget.config(fg=self.theme_colors["accent"])
                        else:
                            widget.config(fg=self.theme_colors["text"])

            # 초기 색상 설정
            mode_frame.after(10, update_radio_colors)
        else:  # cycle
            # 순환매매법 기본값 설정
            mode_var = tk.StringVar(value="long")

            # 순환매매법용 라디오 버튼
            cycle_radio_configs = [
                ("롱만", "long", self.theme_colors["text"]),  # 흰색 텍스트로 변경
                ("조건부 진입", "conditional", self.theme_colors["info"])
            ]

            # 클로저 문제 해결을 위해 개별적으로 라디오 버튼 생성
            # 롱만 라디오 버튼
            def create_long_radio():
                def on_select():
                    mode_var.set("long")
                    # 모든 라디오 버튼 색상 리셋
                    for widget in mode_frame.winfo_children():
                        if isinstance(widget, tk.Radiobutton):
                            widget.config(fg=self.theme_colors["text"])
                    # 선택된 버튼만 노란색으로 변경
                    rb.config(fg=self.theme_colors["accent"])

                rb = tk.Radiobutton(mode_frame, text="롱만", variable=mode_var, value="long",
                                   bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                                   selectcolor="black", activebackground=self.theme_colors["panel_bg"],
                                   activeforeground=self.theme_colors["text"], font=("Segoe UI", 10),
                                   relief="flat", borderwidth=0, indicatoron=1, command=on_select)
                return rb

            # 조건부 진입 라디오 버튼
            def create_conditional_radio():
                def on_select():
                    mode_var.set("conditional")
                    # 모든 라디오 버튼 색상 리셋
                    for widget in mode_frame.winfo_children():
                        if isinstance(widget, tk.Radiobutton):
                            widget.config(fg=self.theme_colors["text"])
                    # 선택된 버튼만 노란색으로 변경
                    rb.config(fg=self.theme_colors["accent"])

                rb = tk.Radiobutton(mode_frame, text="조건부 진입", variable=mode_var, value="conditional",
                                   bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                                   selectcolor="black", activebackground=self.theme_colors["panel_bg"],
                                   activeforeground=self.theme_colors["text"], font=("Segoe UI", 10),
                                   relief="flat", borderwidth=0, indicatoron=1, command=on_select)
                return rb

            # 라디오 버튼 생성 및 배치
            long_rb = create_long_radio()
            long_rb.pack(side="left", padx=(0, 15))

            conditional_rb = create_conditional_radio()
            conditional_rb.pack(side="left", padx=(0, 15))

            # 초기 선택 상태 설정
            def update_cycle_radio_colors():
                current_value = mode_var.get()
                for widget in mode_frame.winfo_children():
                    if isinstance(widget, tk.Radiobutton):
                        widget_value = widget.cget("value")
                        if widget_value == current_value:
                            widget.config(fg=self.theme_colors["accent"])
                        else:
                            widget.config(fg=self.theme_colors["text"])

            # 초기 색상 설정
            mode_frame.after(10, update_cycle_radio_colors)

        # 고급 설정 영역
        advanced_container = tk.Frame(card_frame, bg=self.theme_colors["panel_bg"])
        advanced_container.pack(fill="x", pady=(10, 0))

        # 고급 설정 토글 버튼 - 포지션 모드 바로 아래로 이동
        toggle_frame = tk.Frame(mode_row, bg=self.theme_colors["panel_bg"])
        toggle_frame.pack(side="right", padx=(20, 0))

        advanced_frame = tk.Frame(advanced_container, bg=self.theme_colors["hover"])
        advanced_visible = False

        # 기본 높이 저장 변수
        basic_card_height = None

        def toggle_advanced():
            nonlocal advanced_visible, basic_card_height

            if advanced_visible:
                # 고급설정 접기
                advanced_frame.pack_forget()
                toggle_btn.config(text="고급 설정 펼치기")

                # 저장된 기본 높이로 고정
                if basic_card_height is not None:
                    card_frame.config(height=basic_card_height)
                    card_frame.pack_propagate(False)  # 자동 크기 조정 비활성화

                    # 레이아웃 업데이트
                    card_frame.update_idletasks()
                    self.coins_frame.update_idletasks()
                    self.canvas.update_idletasks()
                    self.canvas.configure(scrollregion=self.canvas.bbox("all"))

                    pass  # 로그 제거됨

            else:
                # 기본 높이 저장 (처음 펼칠 때만)
                if basic_card_height is None:
                    card_frame.update_idletasks()
                    basic_card_height = card_frame.winfo_reqheight()
                    pass  # 로그 제거됨

                # 고급설정 펼치기
                card_frame.pack_propagate(True)  # 자동 크기 조정 활성화
                advanced_frame.pack(fill="x", padx=10, pady=10)
                toggle_btn.config(text="고급 설정 접기")

                # 레이아웃 업데이트
                advanced_frame.update_idletasks()
                advanced_container.update_idletasks()
                card_frame.update_idletasks()
                self.coins_frame.update_idletasks()
                self.canvas.update_idletasks()
                self.canvas.configure(scrollregion=self.canvas.bbox("all"))

                pass  # 로그 제거됨

            advanced_visible = not advanced_visible

        toggle_btn = tk.Button(toggle_frame, text="고급 설정 펼치기",
                              font=("Segoe UI", 9), bg=self.theme_colors["info"],
                              fg=self.theme_colors["text_white"], relief="flat",
                              padx=10, pady=4, cursor="hand2", bd=0,
                              highlightthickness=0, command=toggle_advanced)
        toggle_btn.pack()

        # 레버리지 및 마진 설정
        leverage_row = tk.Frame(advanced_frame, bg=self.theme_colors["hover"])
        leverage_row.pack(fill="x", pady=5, padx=10)

        tk.Label(leverage_row, text="레버리지", font=("Segoe UI", 10, "bold"),
                bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))

        leverage_entry = tk.Entry(leverage_row, width=10, font=("Segoe UI", 10),
                                 bg=self.theme_colors["input_bg"], fg="white", insertbackground="white")
        leverage_entry.pack(side="left", padx=(0, 20))

        margin_var = tk.StringVar(value=data.get("margin_type", "CROSS") if data else "CROSS")

        def toggle_margin_type():
            current = margin_var.get()
            margin_var.set("ISOLATED" if current == "CROSS" else "CROSS")
            btn_margin.config(text=f"{margin_var.get()}")

        btn_margin = tk.Button(leverage_row, text=f"{margin_var.get()}",
                              font=("Segoe UI", 9), command=toggle_margin_type,
                              bg=self.theme_colors["secondary"], fg=self.theme_colors["text"],
                              relief="flat", padx=10, pady=5, cursor="hand2", bd=0,
                              highlightthickness=0)
        btn_margin.pack(side="left")

        # 전략별 고급 설정 복원
        custom_block_container = None
        cycle_entries = None
        take_profit_entry = None
        entry_threshold_entry = None
        stop_loss_entry = None
        sl_option_var = None

        if strategy_type == "grid":
            # 거미줄 매매법 고급 설정
            grid_settings = tk.Frame(advanced_frame, bg=self.theme_colors["hover"])
            grid_settings.pack(fill="x", pady=10, padx=15)

            # 커스텀 레벨 설정
            custom_frame = tk.Frame(grid_settings, bg=self.theme_colors["hover"], relief="solid", borderwidth=1)
            custom_frame.pack(fill="x", pady=5)

            tk.Label(custom_frame, text="커스텀 레벨 설정", font=("Segoe UI", 11, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(pady=5)

            # 헤더
            header_frame = tk.Frame(custom_frame, bg=self.theme_colors["hover"])
            header_frame.pack(fill="x", padx=10)
            tk.Label(header_frame, text="레벨(%)", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).grid(row=0, column=0, padx=10)
            tk.Label(header_frame, text="배수", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).grid(row=0, column=1, padx=10)

            custom_block_container = tk.Frame(custom_frame, bg=self.theme_colors["hover"])
            custom_block_container.pack(fill="x", padx=10, pady=5)

            # 추가 버튼
            tk.Button(custom_frame, text="레벨 추가", font=("Segoe UI", 9),
                     bg=self.theme_colors["primary"], fg=self.theme_colors["text_dark"],
                     relief="flat", padx=10, pady=5, cursor="hand2", bd=0,
                     highlightthickness=0,
                     command=lambda: self.add_custom_block(custom_block_container)).pack(pady=5)

            # 목표 수익률
            profit_row = tk.Frame(grid_settings, bg=self.theme_colors["hover"])
            profit_row.pack(fill="x", pady=5)

            tk.Label(profit_row, text="목표 수익률 (%)", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))

            take_profit_entry = tk.Entry(profit_row, width=12, font=("Segoe UI", 10),
                                        bg=self.theme_colors["input_bg"], fg="white", insertbackground="white")
            take_profit_entry.insert(0, "0.45")
            take_profit_entry.pack(side="left")

            # 진입 변동성 기준
            threshold_row = tk.Frame(grid_settings, bg=self.theme_colors["hover"])
            threshold_row.pack(fill="x", pady=5)

            tk.Label(threshold_row, text="진입 변동성 기준 (%)", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))

            entry_threshold_entry = tk.Entry(threshold_row, width=12, font=("Segoe UI", 10),
                                            bg=self.theme_colors["input_bg"], fg="white", insertbackground="white",
                                            readonlybackground=self.theme_colors["input_bg"])
            entry_threshold_entry.insert(0, "0.1")
            entry_threshold_entry.pack(side="left")

            # 모드 변경에 따른 진입 변동성 기준 처리
            def on_mode_change(*args):
                if mode_var.get() in ("long", "short"):
                    # 롱/숏 모드: 0으로 고정, readonly 상태, 회색 텍스트
                    entry_threshold_entry.config(state="normal")
                    entry_threshold_entry.delete(0, tk.END)
                    entry_threshold_entry.insert(0, "0")
                    entry_threshold_entry.config(state="readonly", fg="gray")
                elif mode_var.get() == "conditional":
                    # 조건부 진입 모드: 0으로 고정, readonly 상태, 회색 텍스트
                    entry_threshold_entry.config(state="normal")
                    entry_threshold_entry.delete(0, tk.END)
                    entry_threshold_entry.insert(0, "0")
                    entry_threshold_entry.config(state="readonly", fg="gray")
                else:
                    # 둘다 모드: 입력 가능, normal 상태, 흰색 텍스트
                    entry_threshold_entry.config(state="normal", fg="white")
                    # 기본값으로 0.1 설정 (둘다 모드로 변경 시)
                    if entry_threshold_entry.get() == "0":
                        entry_threshold_entry.delete(0, tk.END)
                        entry_threshold_entry.insert(0, "0.1")

            mode_var.trace_add("write", on_mode_change)
            on_mode_change()

        else:
            # 순환매수매매 고급 설정
            cycle_settings = tk.Frame(advanced_frame, bg=self.theme_colors["hover"])
            cycle_settings.pack(fill="x", pady=10, padx=15)

            # 레이어 갯수
            layer_row = tk.Frame(cycle_settings, bg=self.theme_colors["hover"])
            layer_row.pack(fill="x", pady=5)
            tk.Label(layer_row, text="레이어 갯수", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))
            cycle_layer_count_entry = tk.Entry(layer_row, width=12, font=("Segoe UI", 10),
                                              bg=self.theme_colors["input_bg"], fg="white", insertbackground="white")
            cycle_layer_count_entry.insert(0, "50")
            cycle_layer_count_entry.pack(side="left")

            # 최하단 레이어
            bottom_row = tk.Frame(cycle_settings, bg=self.theme_colors["hover"])
            bottom_row.pack(fill="x", pady=5)
            tk.Label(bottom_row, text="최하단 레이어 (%)", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))
            cycle_bottom_layer_entry = tk.Entry(bottom_row, width=12, font=("Segoe UI", 10),
                                               bg=self.theme_colors["input_bg"], fg="white", insertbackground="white")
            cycle_bottom_layer_entry.insert(0, "80.0")
            cycle_bottom_layer_entry.pack(side="left")

            # 부분 목표 수익률
            partial_row = tk.Frame(cycle_settings, bg=self.theme_colors["hover"])
            partial_row.pack(fill="x", pady=5)
            tk.Label(partial_row, text="부분 목표 수익률 (%)", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))
            cycle_partial_tp_pct_entry = tk.Entry(partial_row, width=12, font=("Segoe UI", 10),
                                                 bg=self.theme_colors["input_bg"], fg="white", insertbackground="white")
            cycle_partial_tp_pct_entry.insert(0, "1.0")
            cycle_partial_tp_pct_entry.pack(side="left")

            # 최종 목표 수익률
            final_row = tk.Frame(cycle_settings, bg=self.theme_colors["hover"])
            final_row.pack(fill="x", pady=5)
            tk.Label(final_row, text="최종 목표 수익률 (%)", font=("Segoe UI", 10, "bold"),
                    bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(side="left", padx=(0, 10))
            cycle_final_tp_pct_entry = tk.Entry(final_row, width=12, font=("Segoe UI", 10),
                                               bg=self.theme_colors["input_bg"], fg="white", insertbackground="white")
            cycle_final_tp_pct_entry.insert(0, "2.0")
            cycle_final_tp_pct_entry.pack(side="left")

            cycle_entries = (cycle_layer_count_entry, cycle_bottom_layer_entry,
                           cycle_partial_tp_pct_entry, cycle_final_tp_pct_entry)

        # 손절 설정
        sl_settings = tk.Frame(advanced_frame, bg=self.theme_colors["hover"])
        sl_settings.pack(fill="x", pady=10, padx=15)

        tk.Label(sl_settings, text="손절 조건", font=("Segoe UI", 11, "bold"),
                bg=self.theme_colors["hover"], fg=self.theme_colors["text"]).pack(anchor="w", pady=(0, 5))

        sl_option_var = tk.StringVar(value="none")
        sl_frame = tk.Frame(sl_settings, bg=self.theme_colors["hover"])
        sl_frame.pack(fill="x", pady=5)

        tk.Radiobutton(sl_frame, text="없음", variable=sl_option_var, value="none",
                      bg=self.theme_colors["hover"], fg=self.theme_colors["text"],
                      font=("Segoe UI", 10), selectcolor="black").pack(side="left", padx=(0, 20))
        tk.Radiobutton(sl_frame, text="사용자 지정", variable=sl_option_var, value="custom",
                      bg=self.theme_colors["hover"], fg=self.theme_colors["text"],
                      font=("Segoe UI", 10), selectcolor="black").pack(side="left")

        # 손절 퍼센트 입력
        sl_input_frame = tk.Frame(sl_settings, bg=self.theme_colors["hover"])
        sl_input_frame.pack(fill="x", pady=5)

        stop_loss_label = tk.Label(sl_input_frame, text="손절 퍼센트 (%)", font=("Segoe UI", 10, "bold"),
                                  bg=self.theme_colors["hover"], fg=self.theme_colors["text"])
        stop_loss_entry = tk.Entry(sl_input_frame, width=12, font=("Segoe UI", 10),
                                  bg=self.theme_colors["input_bg"], fg="white", insertbackground="white")
        stop_loss_entry.insert(0, "3.0")

        def toggle_stoploss_input(*args):
            if sl_option_var.get() == "custom":
                stop_loss_label.pack(side="left", padx=(0, 10))
                stop_loss_entry.pack(side="left")
            else:
                stop_loss_label.pack_forget()
                stop_loss_entry.pack_forget()

        sl_option_var.trace_add("write", toggle_stoploss_input)
        toggle_stoploss_input()  # 초기 상태 설정

        # 데이터 로딩
        if data:
            # 기본 정보 로딩

            symbol_entry.config(state="normal")
            symbol_entry.delete(0, tk.END)
            symbol_entry.insert(0, data.get("symbol", ""))
            symbol_entry.config(state="readonly")

            fraction_entry.delete(0, tk.END)
            fraction_entry.insert(0, str(data.get("fraction", "100")))

            leverage_entry.delete(0, tk.END)
            leverage_entry.insert(0, str(data.get("leverage", "10" if strategy_type == "grid" else "1")))

            margin_var.set(data.get("margin_type", "CROSS"))

            if strategy_type == "grid":
                # 거미줄 매매법 데이터 로딩
                take_profit_entry.delete(0, tk.END)
                take_profit_entry.insert(0, str(data.get("take_profit_pct", "0.45")))

                entry_threshold_entry.delete(0, tk.END)
                entry_threshold_entry.insert(0, str(data.get("entry_threshold_pct", "0.1")))

                # 거미줄 매매법 커스텀 레벨 복원
                custom_levels = data.get("custom_levels", [])
                custom_multipliers = data.get("custom_multipliers", [])
                if len(custom_levels) > 0 and len(custom_multipliers) > 0:
                    for level, multiplier in zip(custom_levels, custom_multipliers):
                        self.add_custom_block(custom_block_container, level_value=level, multiplier_value=multiplier)
                else:
                    # 거미줄 매매법 기본 레벨 추가
                    self.add_custom_block(custom_block_container, level_value=0, multiplier_value=1, deletable=False)
            else:
                # 순환매매법 데이터 로딩
                cycle_layer_count_entry.delete(0, tk.END)
                cycle_layer_count_entry.insert(0, str(data.get("cycle_layer_count", "3")))

                cycle_bottom_layer_entry.delete(0, tk.END)
                cycle_bottom_layer_entry.insert(0, str(data.get("cycle_bottom_layer_pct", "80.0")))

                cycle_partial_tp_pct_entry.delete(0, tk.END)
                cycle_partial_tp_pct_entry.insert(0, str(data.get("cycle_partial_tp_pct", "1.0")))

                cycle_final_tp_pct_entry.delete(0, tk.END)
                cycle_final_tp_pct_entry.insert(0, str(data.get("cycle_final_tp_pct", "2.0")))

            # 전략별 기본값 설정 (모든 전략 공통)
            if strategy_type == "grid":
                mode_var.set(data.get("mode", "both"))
                # 거미줄 매매법 색상 업데이트
                mode_frame.after(100, update_radio_colors)
            else:  # cycle
                loaded_mode = data.get("mode", "long")
                mode_var.set(loaded_mode)
                # 순환매매법 색상 업데이트 (더 긴 지연)
                mode_frame.after(200, update_cycle_radio_colors)
                # 순환매수매매 데이터 로딩
                cycle_layer_count_entry.delete(0, tk.END)
                cycle_layer_count_entry.insert(0, str(data.get("cycle_layer_count", "50")))

                cycle_bottom_layer_entry.delete(0, tk.END)
                cycle_bottom_layer_entry.insert(0, str(data.get("cycle_bottom_layer_pct", "80.0")))

                cycle_partial_tp_pct_entry.delete(0, tk.END)
                cycle_partial_tp_pct_entry.insert(0, str(data.get("cycle_partial_tp_pct", "1.0")))

                cycle_final_tp_pct_entry.delete(0, tk.END)
                cycle_final_tp_pct_entry.insert(0, str(data.get("cycle_final_tp_pct", "2.0")))

            # 손절 설정 로딩
            stop_loss_val = data.get("stop_loss_pct")
            if stop_loss_val is not None:
                sl_option_var.set("custom")
                stop_loss_entry.delete(0, tk.END)
                stop_loss_entry.insert(0, str(stop_loss_val))
            else:
                sl_option_var.set("none")

        else:
            # 새 추가일 때 기본값 설정
            if strategy_type == "grid":
                # 거미줄 매매법: 기본 커스텀 레벨 설정
                self.add_custom_block(custom_block_container, level_value=0, multiplier_value=1, deletable=False)
                mode_var.set("both")  # 기본값: 둘다
                # 색상 업데이트
                mode_frame.after(50, update_radio_colors)
            else:
                # 순환매매법: 기본값 설정
                mode_var.set("long")  # 기본값: 롱만
                # 순환매매법에서는 커스텀 레벨 불필요 (고정된 레이어 설정 사용)
                # 색상 업데이트
                mode_frame.after(200, update_cycle_radio_colors)

        # 프레임 및 엔트리 저장
        self.coin_frames.append(card_frame)
        if strategy_type == "grid":
            self.coin_entries.append((
                symbol_entry, fraction_entry, mode_var, leverage_entry,
                custom_block_container, None, margin_var, take_profit_entry,
                entry_threshold_entry, is_saved, stop_loss_entry, sl_option_var
            ))
        else:
            self.coin_entries.append((
                symbol_entry, fraction_entry, mode_var, leverage_entry,
                None, cycle_entries, margin_var, None, None,
                is_saved, stop_loss_entry, sl_option_var
            ))




    def remove_coin_row(self):
        if self.coin_frames:
            frame = self.coin_frames.pop()
            frame.destroy()
            self.coin_entries.pop()

    def load_settings(self):
        try:
            res = requests.get(f"{API_BASE_URL}/settings/{self.username}", headers=API_HEADERS)
            if res.status_code == 200:
                try:
                    data = res.json()
                except Exception as e:
                    data = {
                    "strategy_type": "grid",  # 기본 전략 유형만 설정
                    "grid": {
                        "coins": []
                    },
                    "cycle": {
                        "coins": []
                    },
                }
                self.current_settings = data  # ✅ settings 전체를 메모리에 저장

                strategy_type = data.get("strategy_type", "grid")
                self.strategy_type_var.set("거미줄 매매법" if strategy_type == "grid" else "순환매수매매 기법")

                coins_data = data.get(strategy_type, {}).get("coins", [])

                # 기존 coin rows 지우기
                for frame in self.coin_frames:
                    frame.destroy()
                self.coin_frames.clear()
                self.coin_entries.clear()
                
                for coin in coins_data:
                    self.add_coin_row(data=coin, strategy_type=strategy_type, is_saved=True)

            else:
                messagebox.showerror("실패", f"설정 불러오기 실패: {res.text}")
        except Exception as e:
            logging.exception("설정 로드 실패")
            messagebox.showerror("오류", f"설정 불러오기 실패: {e}")

    def save_settings(self):
        try:
            res = requests.get(f"{API_BASE_URL}/settings/{self.username}", headers=API_HEADERS)
            if res.status_code != 200:
                messagebox.showerror("실패", f"기존 설정 불러오기 실패: {res.text}")
                return

            existing_settings = res.json() or {}
            coins = []
            total_fraction = 0

            selected_strategy = "grid" if self.strategy_type_var.get() == "거미줄 매매법" else "cycle"

            for idx, entries in enumerate(self.coin_entries, start=1):
                symbol_entry = entries[0]
                for child in entries:
                    if isinstance(child, tk.Entry) and child is not symbol_entry:
                        val = child.get().strip()
                        if val:
                            try:
                                num = float(val)
                                if num < 0:
                                    messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: {val}은 0보다 작을 수 없습니다.")
                                    return
                            except ValueError:
                                messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: '{val}'은 숫자가 아닙니다.")
                                return

                if selected_strategy == "grid":
                    (
                        symbol_entry, fraction_entry, mode_var, leverage_entry,
                        custom_block_container, _, margin_var, take_profit_entry,
                        entry_threshold_entry, is_saved, stop_loss_entry, sl_option_var
                    ) = entries
                else:
                    (
                        symbol_entry, fraction_entry, mode_var, leverage_entry,
                        _, cycle_entries, margin_var, _, _, is_saved, stop_loss_entry, sl_option_var
                    ) = entries
                    cycle_layer_count_entry, cycle_bottom_layer_entry, cycle_partial_tp_pct_entry, cycle_final_tp_pct_entry = cycle_entries

                symbol = symbol_entry.get().strip()
                fraction = fraction_entry.get().strip()
                leverage = leverage_entry.get().strip()

                if not symbol:
                    messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 거래 심볼이 비어 있습니다.")
                    return
                if not fraction:
                    messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 사용 비율이 비어 있습니다.")
                    return
                if not leverage:
                    messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 레버리지가 비어 있습니다.")
                    return

                mode = mode_var.get()
                leverage = int(leverage)
                fraction = float(fraction)
                total_fraction += fraction

                if selected_strategy == "grid":
                    custom_levels = []
                    custom_multipliers = []

                    if custom_block_container:
                        for block in custom_block_container.winfo_children():
                            widgets = block.winfo_children()
                            if len(widgets) >= 2:
                                level = widgets[0].get().strip()
                                multiplier = widgets[1].get().strip()
                                if not level or not multiplier:
                                    messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 커스텀 레벨 또는 배수에 빈 값이 있습니다.")
                                    return
                                custom_levels.append(float(level))
                                custom_multipliers.append(float(multiplier))

                    if not custom_levels or not custom_multipliers:
                        messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 커스텀 레벨/배수가 설정되지 않았습니다.")
                        return

                    take_profit = take_profit_entry.get().strip()
                    if not take_profit:
                        messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 목표 수익률이 비어 있습니다.")
                        return

                    if sl_option_var.get() == "custom":
                        stop_loss = stop_loss_entry.get().strip()
                        try:
                            stop_loss_val = float(stop_loss)
                            if stop_loss_val <= 0 or stop_loss_val > 100:
                                raise ValueError
                        except:
                            messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 손절 퍼센트는 0보다 크고 100 이하의 숫자여야 합니다.")
                            return
                    else:
                        stop_loss_val = None

                    entry_threshold_raw = entry_threshold_entry.get().strip()
                    if not entry_threshold_raw:
                        messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 진입 변동성 기준이 비어 있습니다.")
                        return

                    coin_data = {
                        "symbol": symbol,
                        "fraction": fraction,
                        "mode": mode,
                        "leverage": leverage,
                        "custom_levels": custom_levels,
                        "custom_multipliers": custom_multipliers,
                        "poll_interval": 1.0,
                        "take_profit_pct": float(take_profit),
                        "entry_threshold_pct": float(entry_threshold_raw),
                        "stop_loss_pct": stop_loss_val,
                        "margin_type": margin_var.get()
                    }

                else:
                    if sl_option_var is not None and sl_option_var.get() == "custom":
                        stop_loss = stop_loss_entry.get().strip()
                        try:
                            stop_loss_val = float(stop_loss)
                            if stop_loss_val <= 0 or stop_loss_val > 100:
                                raise ValueError
                        except:
                            messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 손절 퍼센트는 0보다 크고 100 이하의 숫자여야 합니다.")
                            return
                    else:
                        stop_loss_val = None

                    try:
                        layer_count = int(cycle_layer_count_entry.get())
                        if layer_count <= 0:
                            raise ValueError
                    except ValueError:
                        messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 레이어 개수는 1 이상의 정수여야 합니다.")
                        return

                    try:
                        partial_tp = float(cycle_partial_tp_pct_entry.get())
                        if partial_tp == 0:
                            raise ValueError
                    except ValueError:
                        messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 부분 목표 수익률은 0 이외의 숫자여야 합니다.")
                        return

                    try:
                        final_tp = float(cycle_final_tp_pct_entry.get())
                        if final_tp < 0:
                            raise ValueError
                    except ValueError:
                        messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 최종 목표 수익률은 0 이상 숫자여야 합니다.")
                        return

                    try:
                        bottom_pct = float(cycle_bottom_layer_entry.get())
                        if bottom_pct < 0:
                            raise ValueError
                    except ValueError:
                        messagebox.showerror("입력 오류", f"{idx}번째 코인 블럭: 최하단 레이어 퍼센트는 0 이상 숫자여야 합니다.")
                        return

                    if partial_tp >= final_tp:
                        messagebox.showwarning("주의", f"{idx}번째 코인 블럭: 부분 목표 수익률({partial_tp}%)이 최종 목표 수익률({final_tp}%)보다 크거나 같습니다.")
                        return
                    if partial_tp >= bottom_pct:
                        messagebox.showwarning("주의", f"{idx}번째 코인 블럭: 부분 목표 수익률({partial_tp}%)이 최하단 레이어 퍼센트({bottom_pct}%)보다 크거나 같습니다.")
                        return
                    coin_data = {
                        "symbol": symbol,
                        "fraction": fraction,
                        "mode": mode,  # 하드코딩된 "long" → 사용자 선택값 사용
                        "leverage": leverage,
                        "custom_levels": [1],
                        "custom_multipliers": [1.0],
                        "poll_interval": 1.0,
                        "cycle_layer_count": layer_count,
                        "cycle_bottom_layer_pct": bottom_pct,
                        "cycle_partial_tp_pct": partial_tp,
                        "cycle_final_tp_pct": final_tp,
                        "stop_loss_pct": stop_loss_val,
                        "margin_type": margin_var.get()
                    }

                coins.append(coin_data)

            if total_fraction > 100:
                messagebox.showerror("입력 오류", f"{'거미줄 매매법' if selected_strategy == 'grid' else '순환매수매매 기법'} 사용 비율 합이 100을 초과합니다!")
                return
            if 100 >= total_fraction > 95:
                messagebox.showerror("권장사항", f"{'거미줄 매매법' if selected_strategy == 'grid' else '순환매수매매 기법'} 원활한 매매를 위해선 사용 비율 합이 95 이하가 되는것이 좋습니다")
                return

            existing_settings.setdefault('grid',  {})
            existing_settings.setdefault('cycle', {})
            existing_settings['grid'].setdefault('coins', [])
            existing_settings['cycle'].setdefault('coins', [])

            existing_settings[selected_strategy]['coins'] = coins
            existing_settings['username'] = self.username
            existing_settings['strategy_type'] = selected_strategy

            res = requests.post(f"{API_BASE_URL}/settings", json=existing_settings, headers=API_HEADERS)

            if res.status_code == 200:
                messagebox.showinfo("성공", "설정이 저장되었습니다.\n(전략이 실행 중이라면, 전략을 다시 실행해야 최신 설정이 적용됩니다.)")
            else:
                messagebox.showerror("실패", f"설정 저장 실패: {res.text}")

        except Exception as e:
            logging.exception("설정 저장 실패")
            messagebox.showerror("오류", f"설정 저장 실패: {e}")

    def refresh_positions(self):
        """포지션 정보 강제 새로고침"""
        try:
            # 즉시 포지션 정보 업데이트
            self.update_strategy_info()
            print("포지션 정보 새로고침 완료")
        except Exception as e:
            print(f"포지션 새로고침 실패: {e}")
            messagebox.showerror("오류", f"포지션 새로고침 실패: {e}")

    def get_symbol_filters(self, symbols):
        """바이낸스 API에서 심볼 필터 정보 조회"""
        try:
            from binance.client import Client
            client = Client()
            info = client.futures_exchange_info()

            filters_data = {}
            for symbol_info in info['symbols']:
                if symbol_info['symbol'] in symbols:
                    filters = {f['filterType']: f for f in symbol_info['filters']}
                    filters_data[symbol_info['symbol']] = {
                        "minQty": float(filters["LOT_SIZE"]["minQty"]),
                        "minNotional": float(filters["MIN_NOTIONAL"]["notional"]),
                        "stepSize": float(filters["LOT_SIZE"]["stepSize"]),
                        "tickSize": float(filters["PRICE_FILTER"]["tickSize"]),
                        "minPrice": float(filters["PRICE_FILTER"]["minPrice"]),
                    }
            return filters_data
        except Exception as e:
            print(f"필터 정보 조회 실패: {e}")
            return {}

    def run_strategy(self):
        proceed = messagebox.askokcancel("전략 실행", "마지막으로 저장한 설정 기반으로 전략이 실행됩니다.\n계속 진행할까요?")
        if not proceed:
            return
        try:
            # 서버에서 현재 설정 읽어오기
            res = requests.get(f"{API_BASE_URL}/settings/{self.username}", headers=API_HEADERS)
            if res.status_code != 200:
                messagebox.showerror("실패", f"설정 불러오기 실패: {res.text}")
                return

            settings = res.json()

            selected_strategy = "grid" if self.strategy_type_var.get() == "거미줄 매매법" else "cycle"
            coins_data = settings.get(selected_strategy, {}).get("coins", [])

            if not coins_data:
                messagebox.showerror("실패", "선택한 매매법에 코인 설정이 없습니다.")
                return

            # 전략에 사용될 코인 목록 추출
            symbols = [coin.get("symbol", "").upper() for coin in coins_data if coin.get("symbol")]

            if symbols:
                # 바이낸스 API에서 필터 정보 조회
                print(f"필터 정보 조회 중: {symbols}")
                filters_data = self.get_symbol_filters(symbols)

                if filters_data:
                    # 서버에 필터 정보 전송
                    filter_res = requests.post(f"{API_BASE_URL}/symbol_filters",
                                             json=filters_data, headers=API_HEADERS)
                    if filter_res.status_code == 200:
                        print("필터 정보 서버 전송 완료")
                    else:
                        print(f"필터 정보 전송 실패: {filter_res.text}")

                # 조건부 진입을 사용하는 코인들의 캔들 데이터 수집 및 전송
                conditional_symbols = []
                print("코인별 모드 확인:")
                for coin in coins_data:
                    symbol = coin.get("symbol", "")
                    mode = coin.get("mode", "")
                    print(f"  {symbol}: mode={mode}")
                    if coin.get("mode") == "conditional":  # position_mode → mode로 수정
                        conditional_symbols.append(coin["symbol"])

                if conditional_symbols:
                    print(f"조건부 진입 심볼들: {conditional_symbols}")
                    candle_data = self.collect_candle_data(conditional_symbols)
                    print(f"수집된 캔들 데이터: {list(candle_data.keys()) if candle_data else '없음'}")
                    if candle_data:
                        candle_res = requests.post(f"{API_BASE_URL}/candle_data", json=candle_data, headers=API_HEADERS)
                        if candle_res.status_code == 200:
                            print("캔들 데이터 서버 전송 완료")
                        else:
                            print(f"캔들 데이터 전송 실패: {candle_res.text}")
                    else:
                        print("캔들 데이터 수집 실패 - 데이터가 비어있음")

            # 서버에 전략 실행 요청 보내기
            payload = {
                "username": self.username,
                "coins": coins_data,
                "strategy_type": selected_strategy
            }

            res = requests.post(f"{API_BASE_URL}/run_strategy", json=payload, headers=API_HEADERS)
            if res.status_code == 200:
                messagebox.showinfo("전략 실행", "전략이 실행되었습니다.")
                self.is_running = True
                self.update_control_buttons()  # ✅ 버튼 상태 업데이트
                self.update_strategy_info()
            else:
                messagebox.showerror("실패", f"전략 실행 실패: {res.text}")

        except Exception as e:
            messagebox.showerror("오류", f"전략 실행 중 오류 발생: {e}")

    def collect_candle_data(self, symbols):
        """조건부 진입을 위한 캔들 데이터 수집"""
        try:
            from datetime import datetime, timedelta

            candle_data = {}

            for symbol in symbols:
                try:
                    # 바이낸스 API로 4시간봉 데이터 조회 (최근 100개)
                    url = f"https://api.binance.com/api/v3/klines"
                    params = {
                        "symbol": symbol,
                        "interval": "4h",
                        "limit": 100
                    }

                    response = requests.get(url, params=params, timeout=10)
                    if response.status_code == 200:
                        klines = response.json()

                        # 캔들 데이터 변환
                        candles = []
                        for kline in klines:
                            candle = {
                                "timestamp": int(kline[0]),
                                "open": float(kline[1]),
                                "high": float(kline[2]),
                                "low": float(kline[3]),
                                "close": float(kline[4]),
                                "volume": float(kline[5])
                            }
                            candles.append(candle)

                        candle_data[symbol] = {
                            "timeframe": "4h",
                            "candles": candles,
                            "last_update": int(datetime.now().timestamp() * 1000)
                        }

                        print(f"{symbol} 캔들 데이터 수집 완료: {len(candles)}개")
                    else:
                        print(f"{symbol} 캔들 데이터 조회 실패: {response.status_code}")

                except Exception as e:
                    print(f"{symbol} 캔들 데이터 수집 오류: {e}")
                    continue

            return candle_data

        except Exception as e:
            print(f"캔들 데이터 수집 전체 오류: {e}")
            return {}

    def stop_strategy(self):
        try:
            close_positions = messagebox.askyesno(
                "포지션 종료 여부",
                "현재 존재하는 포지션을 모두 종료하시겠습니까?"
            )

            payload = {"username": self.username, "close_positions": close_positions}
            res = requests.post(f"{API_BASE_URL}/stop_strategy", json=payload, headers=API_HEADERS)

            if res.status_code == 200:
                messagebox.showinfo("전략 중지", "전략이 중지되었습니다.")
                self.is_running = False
                self.update_control_buttons()  # ✅ 버튼 상태 업데이트
                self.update_strategy_info()
            else:
                messagebox.showerror("실패", f"전략 중지 실패: {res.text}")

        except Exception as e:
            logging.exception("전략 중지 실패")
            messagebox.showerror("오류", f"전략 중지 중 오류 발생: {e}")

    def stop_after_profit(self):
        try:
            res = requests.post(f"{API_BASE_URL}/stop_after_profit", json={"username": self.username}, headers=API_HEADERS)
            if res.status_code == 200:
                messagebox.showinfo("예약 완료", "이익 실현 후 전략이 중지됩니다.")
                self.update_strategy_info()
            else:
                messagebox.showerror("실패", f"요청 실패: {res.text}")
        except Exception as e:
            logging.exception("예약 중지 실패")
            messagebox.showerror("오류", f"예약 중지 실패: {e}")
    
    def change_password(self):
        old = sd.askstring("비밀번호 변경", "현재 비밀번호를 입력하세요:", show="*")
        if old is None:
            return
        new = sd.askstring("비밀번호 변경", "새 비밀번호를 입력하세요:", show="*")
        if new is None:
            return
        try:
            res = requests.put(
                f"{API_BASE_URL}/user/password",
                json={"username": self.username, "old_password": old, "new_password": new},
                headers=API_HEADERS
            )
            if res.status_code == 200:
                messagebox.showinfo("성공", "비밀번호가 변경되었습니다.")
            else:
                messagebox.showerror("실패", f"변경 실패: {res.text}")
        except Exception as e:
            logging.exception("비밀번호 변경 실패")
            messagebox.showerror("오류", f"변경 실패: {e}")

    def start_auto_update(self, interval=1.0):
        def loop():
            while True:
                try:
                    self.update_strategy_info()
                except Exception as e:
                    logging.warning(f"전략 정보 자동 갱신 실패: {e}")
                time.sleep(interval)

        threading.Thread(target=loop, daemon=True).start()
    
    def align_colons(self, lines: list[str], padding: int = 2) -> str:
        def display_width(text):
            # 한글은 2, 그 외는 1로 계산
            return sum(2 if ord(c) > 127 else 1 for c in text)

        parts = []
        for line in lines:
            match = re.match(r"^(.*?)(:)(.*)$", line)
            if match:
                left, _, right = match.groups()
                left = left.strip()
                right = right.strip()
                parts.append((left, right))
            else:
                parts.append((line, ""))

        max_display_len = max(display_width(label) for label, _ in parts)

        def pad_display_width(label, target_width):
            result = ""
            width = 0
            for c in label:
                char_width = 2 if ord(c) > 127 else 1
                result += c
                width += char_width
            # 남은 공간에 일반 스페이스 추가
            result += " " * (target_width - width + padding)
            return result

        formatted = [f"{pad_display_width(label, max_display_len)}: {value}" for label, value in parts]
        return "\n".join(formatted)

    def update_strategy_info(self):
        # 🔧 Orders 탭이 활성화되어 있으면 Position 탭 업데이트 건너뛰기
        if hasattr(self, 'current_position_tab') and self.current_position_tab == "orders":
            return

        try:
            res = requests.get(f"{API_BASE_URL}/strategy_status/{self.username}", headers=API_HEADERS)
            if res.status_code != 200:
                logging.warning(f"전략 상태 조회 실패: {res.text}")
                return

            data = res.json()
            is_running_status = data.get("is_running", "값 없음")

            # 실행 상태 업데이트
            self.is_running = False if is_running_status == "not_running" else True
            self.update_control_buttons()

            # 상태 텍스트 설정
            if is_running_status == "running":
                is_running = '🟢 실행중'
            elif is_running_status == "not_running":
                is_running = '🔴 중지됨'
            elif is_running_status == "stop_after_profit":
                is_running = '🟡 이익 후 중지'
            else:
                is_running = '❓ 알 수 없음'
            # 전문적인 계정 정보 포맷팅
            strategy_name = "거미줄 매매법" if data['strategy_type'] == "grid" else "순환 매매법"
            pnl_sign = "+" if data['unrealized_pnl'] >= 0 else ""

            new_text = f"""╔══════════════════════════════════════════════════╗
ACCOUNT DASHBOARD
╠══════════════════════════════════════════════════╣

사용자: {data['username']}
상태: {str(is_running)}

지갑 잔고: {data['wallet_balance']:,.2f} USDT
총 자산: {data['total_usdt']:,.2f} USDT
미실현 손익: {pnl_sign}{data['unrealized_pnl']:,.6f} USDT

╠══════════════════════════════════════════════════╣
STRATEGY INFO
╠══════════════════════════════════════════════════╣

전략: {strategy_name}
거래 코인 ({len(data['symbols'])}개): {', '.join(data['symbols'])}

╚══════════════════════════════════════════════════╝"""

            current_text = self.strategy_info_text.get("1.0", tk.END).strip()
            if current_text != new_text:
                scroll_pos = self.strategy_info_text.yview()  # ✅ 스크롤 위치 저장

                self.strategy_info_text.config(state="normal")
                self.strategy_info_text.delete("1.0", tk.END)

                # 각 줄별로 가운데 정렬 적용
                lines = new_text.split('\n')
                for i, line in enumerate(lines):
                    self.strategy_info_text.insert(tk.END, line, "center")
                    if i < len(lines) - 1:  # 마지막 줄이 아니면 개행 추가
                        self.strategy_info_text.insert(tk.END, '\n')

                self.strategy_info_text.config(state="disabled")

                self.strategy_info_text.yview_moveto(scroll_pos[0])  # ✅ 스크롤 위치 복원

            # ✅ 모든 행 데이터 준비 (깜빡임 방지를 위해)
            all_rows_data = []

            # 1. 실제 포지션이 있는 코인들 표시
            for p in data.get("positions", []):
                symbol = p["symbol"]
                side = p["side"]
                amt = f"{p['amount']:.4f}"
                entry = f"{p['entry_price']:.5f}"
                mark = f"{p['mark_price']:.5f}"
                pnl = f"{p['pnl']:.2f} USDT"
                roi = f"{p['ROI']:+.2f}%"
                # 🔧 주문 정보를 간단한 개수로 표시
                open_orders_raw = p["open_orders"]
                if isinstance(open_orders_raw, list):
                    open_orders = f"{len(open_orders_raw)}개"
                elif isinstance(open_orders_raw, dict):
                    # 딕셔너리인 경우 값들의 합계
                    total_orders = sum(v if isinstance(v, (int, float)) else 0 for v in open_orders_raw.values())
                    open_orders = f"{total_orders}개"
                elif isinstance(open_orders_raw, (int, float)):
                    open_orders = f"{open_orders_raw}개"
                else:
                    open_orders = "0개"
                # 오류 정보 처리
                error_info = data.get("errors", {}).get(symbol, {})
                if isinstance(error_info, dict):
                    error_msg = error_info.get("message", "없음")
                    error_count = error_info.get("count", 0)
                    if error_msg != "없음" and error_count > 0:
                        error_display = f"! ({error_count})"
                    else:
                        error_display = "없음"
                else:
                    error_display = error_info if error_info != "없음" else "없음"

                values = [symbol, side, amt, entry, mark, pnl, roi, open_orders, error_display, "Close"]

                # 색상은 자동으로 계산되므로 None으로 전달
                all_rows_data.append((values, None))

            # 2. 대기중인 코인들 표시 (설정에는 있지만 포지션이 없는 코인들)
            # ✅ 실제 포지션이 있는 코인만 표시하도록 수정 (대기중 표시 제거)
            # 포지션이 없으면 테이블에서 완전히 제거하여 혼동 방지

            # ✅ 모든 행 데이터를 한 번에 업데이트 (깜빡임 방지)
            self.position_table.update_rows(all_rows_data)

            # CustomTable에서 자체적으로 클릭 이벤트 처리

            if not hasattr(self, "_initial_chart_shown"):
                if data.get("positions"):
                    first_symbol = data["positions"][0]["symbol"]
                    self._initial_chart_shown = True
                    threading.Thread(target=lambda: self.open_chart(first_symbol), daemon=True).start()

            # Orders 탭이 활성화되어 있으면 업데이트
            if hasattr(self, 'current_position_tab') and self.current_position_tab == "orders":
                self.update_orders_display()

        except Exception as e:
            print(e)
            logging.exception("전략 정보 업데이트 실패")

    def close_single_position_by_symbol(self, symbol: str):
        symbol = symbol.strip().upper()
        if not symbol:
            messagebox.showerror("입력 오류", "심볼이 비어 있습니다.")
            return

        confirm = messagebox.askyesno(
            "포지션 종료",
            f"{symbol} 포지션을 종료하시겠습니까?\n(전략을 다시 실행하기 전까지 해당 코인 매매는 중지됩니다)"
        )
        if not confirm:
            return

        try:
            res = requests.post(f"{API_BASE_URL}/close_position", json={
                "username": self.username,
                "symbol": symbol
            }, headers=API_HEADERS)
            if res.status_code == 200:
                messagebox.showinfo("성공", f"{symbol} 포지션 종료 완료")
            else:
                messagebox.showerror("실패", f"{symbol} 포지션 종료 실패: {res.text}")
        except Exception as e:
            logging.exception("포지션 종료 실패")
            messagebox.showerror("오류", f"{symbol} 종료 중 오류 발생: {e}")

    def close_single_position(self, symbol_entry):
        symbol = symbol_entry.get().strip().upper()
        if not symbol:
            messagebox.showerror("입력 오류", "심볼이 비어 있습니다.")
            return
        confirm = messagebox.askyesno("포지션 종료", f"{symbol} 포지션을 종료하시겠습니까?\n(포지션을 종료하면, 전략을 다시 실행해기 전까지는 해당 코인의 매매가 이루어지지 않습니다)")
        if not confirm:
            return

        try:
            res = requests.post(f"{API_BASE_URL}/close_position", json={
                "username": self.username,
                "symbol": symbol
            }, headers=API_HEADERS)
            if res.status_code == 200:
                messagebox.showinfo("성공", f"{symbol} 포지션 종료 완료")
            else:
                messagebox.showerror("실패", f"{symbol} 포지션 종료 실패: {res.text}")
        except Exception as e:
            logging.exception("개별 포지션 종료 실패")
            messagebox.showerror("오류", f"{symbol} 종료 중 오류 발생: {e}")

    def show_error_details(self, symbol):
        """오류 상세 정보 팝업 표시"""
        try:
            # 기본 오류 정보 조회
            res = requests.get(f"{API_BASE_URL}/strategy_status/{self.username}", headers=API_HEADERS)
            if res.status_code != 200:
                messagebox.showerror("오류", "오류 정보를 가져올 수 없습니다.")
                return

            data = res.json()
            errors = data.get("errors", {})
            error_info = errors.get(symbol, {})

            # 오류 히스토리 조회
            try:
                history_res = requests.get(f"{API_BASE_URL}/error_history/{self.username}/{symbol}?limit=10", headers=API_HEADERS)
                if history_res.status_code == 200:
                    history_data = history_res.json()
                    error_history = history_data.get("history", [])
                    total_count = history_data.get("total_count", 0)
                else:
                    error_history = []
                    total_count = 0
            except Exception:
                error_history = []
                total_count = 0

            if isinstance(error_info, dict):
                error_msg = error_info.get("message", "오류 정보 없음")
                error_count = error_info.get("count", 0)
                timestamp = error_info.get("timestamp", 0)

                # 타임스탬프를 읽기 쉬운 형태로 변환
                if timestamp:
                    import datetime
                    error_time = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
                else:
                    error_time = "알 수 없음"

                detail_msg = f"심볼: {symbol}\n"
                detail_msg += f"현재 오류: {error_msg}\n"
                detail_msg += f"총 발생 횟수: {error_count}회\n"
                detail_msg += f"마지막 발생: {error_time}\n\n"

                # 오류 히스토리 표시
                if error_history:
                    detail_msg += f"최근 오류 히스토리 (최신 10개, 전체 {total_count}개):\n"
                    detail_msg += "=" * 50 + "\n"
                    for i, hist in enumerate(error_history[:10], 1):
                        hist_time = datetime.datetime.fromtimestamp(hist["timestamp"]).strftime("%m-%d %H:%M:%S")
                        detail_msg += f"{i:2d}. [{hist_time}] {hist['message']}\n"
                    detail_msg += "=" * 50 + "\n\n"

                # 상세 스택트레이스 정보가 있으면 표시
                stack_trace = error_info.get("stack_trace", "")
                if stack_trace:
                    detail_msg += f"최신 오류 상세 정보:\n{stack_trace}\n\n"

                detail_msg += "이 오류를 해결하려면:\n"
                detail_msg += "1. 잔고가 충분한지 확인\n"
                detail_msg += "2. 코인 설정값 확인\n"
                detail_msg += "3. 전략을 다시 시작해보세요"
            else:
                detail_msg = f"심볼: {symbol}\n오류: {error_info}"

            # 오류 상세 정보 창 - 히스토리 표시를 위해 크기 확대
            error_window = tk.Toplevel(self.root)
            error_window.title(f"{symbol} 오류 상세 정보")
            error_window.geometry("700x600")  # 크기 확대
            error_window.configure(bg=self.theme_colors["bg"])

            # 텍스트 위젯
            text_frame = tk.Frame(error_window, bg=self.theme_colors["bg"])
            text_frame.pack(fill="both", expand=True, padx=20, pady=20)

            text_widget = tk.Text(text_frame,
                                bg=self.theme_colors["panel_bg"],
                                fg=self.theme_colors["text"],
                                font=("Segoe UI", 10),
                                wrap="word",
                                state="normal")

            scrollbar = tk.Scrollbar(text_frame, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side="left", fill="both", expand=True)
            # scrollbar.pack(side="right", fill="y")  # 스크롤바 숨김

            text_widget.insert("1.0", detail_msg)
            text_widget.config(state="disabled")

            # 버튼 프레임
            button_frame = tk.Frame(error_window, bg=self.theme_colors["bg"])
            button_frame.pack(fill="x", padx=20, pady=(0, 20))

            # 오류 초기화 버튼
            clear_btn = tk.Button(button_frame, text="오류 초기화",
                                font=("Segoe UI", 10, "bold"),
                                bg=self.theme_colors["warning"], fg="#000000",
                                relief="flat", padx=15, pady=5,
                                command=lambda: self.clear_symbol_error(symbol, error_window))
            clear_btn.pack(side="right", padx=(10, 0))

            # 닫기 버튼
            close_btn = tk.Button(button_frame, text="닫기",
                                font=("Segoe UI", 10, "bold"),
                                bg=self.theme_colors["panel_bg"], fg=self.theme_colors["text"],
                                relief="flat", padx=15, pady=5,
                                command=error_window.destroy)
            close_btn.pack(side="right")

        except Exception as e:
            logging.exception("오류 상세 정보 표시 실패")
            messagebox.showerror("오류", f"오류 정보 표시 실패: {e}")

    def clear_symbol_error(self, symbol, window):
        """특정 심볼의 오류 초기화"""
        try:
            # 서버에 오류 초기화 요청
            res = requests.post(f"{API_BASE_URL}/report_error",
                              json={
                                  "username": self.username,
                                  "symbol": symbol,
                                  "message": "없음"
                              },
                              headers=API_HEADERS)

            if res.status_code == 200:
                messagebox.showinfo("성공", f"{symbol} 오류가 초기화되었습니다.")
                window.destroy()
                # 즉시 포지션 정보 업데이트
                self.update_strategy_info()
            else:
                messagebox.showerror("실패", f"오류 초기화 실패: {res.text}")

        except Exception as e:
            logging.exception("오류 초기화 실패")
            messagebox.showerror("오류", f"오류 초기화 실패: {e}")

    def conditional_strategy_start(self, symbol, conditional_price):
        try:
            target = float(conditional_price)
        except:
            messagebox.showerror("입력 오류", "조건 가격을 올바르게 입력해주세요.")
            return
        messagebox.showinfo("대기 중", f"{symbol} 가격이 {target}에 도달하면 전략을 시작합니다.")

        def check_price_loop():
            while True:
                try:
                    resp = requests.get(f"{API_BASE_URL}/price", params={"symbol": symbol}, headers=API_HEADERS)
                    current_price = float(resp.json().get("price", 0))
                except Exception as e:
                    messagebox.showerror("오류", f"가격 조회 실패: {e}")
                    break

                # 조건 체크 로그 제거
                if current_price == target:
                    messagebox.showinfo("조건 만족", f"{symbol} 현재가 {current_price} → 전략 실행")
                    self.run_strategy()
                    break
                time.sleep(2)

        threading.Thread(target=check_price_loop, daemon=True).start()

    def clear_errors(self):
        """모든 오류 초기화"""
        try:
            res = requests.post(f"{API_BASE_URL}/clear_error",
                              json={"username": self.username},
                              headers=API_HEADERS)
            if res.status_code == 200:
                messagebox.showinfo("성공", "모든 오류가 초기화되었습니다.")
            else:
                messagebox.showerror("오류", "오류 초기화에 실패했습니다.")
        except Exception as e:
            messagebox.showerror("오류", f"오류 초기화 중 오류 발생: {e}")

    def cleanup_all_errors(self):
        """모든 오류 히스토리를 100개로 제한"""
        try:
            result = messagebox.askyesno("오류 정리",
                                       "모든 코인의 오류 히스토리를 100개로 제한하시겠습니까?\n"
                                       "100개를 넘는 오래된 오류들이 삭제됩니다.")
            if not result:
                return

            res = requests.post(f"{API_BASE_URL}/cleanup_all_errors", headers=API_HEADERS)
            if res.status_code == 200:
                data = res.json()
                messagebox.showinfo("성공", data.get("message", "오류 정리가 완료되었습니다."))
            else:
                messagebox.showerror("오류", "오류 정리에 실패했습니다.")
        except Exception as e:
            messagebox.showerror("오류", f"오류 정리 중 오류 발생: {e}")

def open_dashboard(username):
    root = tk.Tk()
    # 아이콘 적용 여기서!

    StrategyGUI(root, username)
    root.mainloop()

if __name__ == "__main__":
    root = tk.Tk()
    LoginWindow(root)
    root.mainloop()


