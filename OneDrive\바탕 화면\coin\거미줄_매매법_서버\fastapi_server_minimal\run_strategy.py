# run_strategy.py
import asyncio
import json
import os
import logging
import signal
import requests
import math
import time
from binance.client import Client
import traceback
from decimal import Decimal
from redis_manager import get_user_status, get_price
from logging.handlers import RotatingFileHandler
import aiohttp
# from collections import defaultdict  # 사용하지 않음
import sys
import redis
try:
    # 현재 스크립트와 같은 디렉토리에서 모듈 import
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # 직접 파일 실행으로 모듈 로드
    import importlib.util
    spec = importlib.util.spec_from_file_location("technical_indicators",
                                                  os.path.join(current_dir, "technical_indicators.py"))
    technical_indicators = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(technical_indicators)
    analyze_conditional_entry = technical_indicators.analyze_conditional_entry
    print("✅ technical_indicators 모듈 로드 성공")

except (ImportError, Exception) as e:
    # 모듈을 찾을 수 없는 경우 더미 함수 정의
    print(f"❌ technical_indicators 모듈 로드 실패: {e}")
    import random
    def analyze_conditional_entry(candles, rsi_long_threshold=20.0, rsi_short_threshold=80.0):
        # 임시: 캔들 데이터가 없으면 대기
        if not candles:
            logging.warning(f"더미 함수 사용 - 캔들 데이터 없음")
            return {
                "signal": "wait",
                "rsi": 50.0,
                "stoch_rsi_k": 50.0,
                "stoch_rsi_d": 50.0,
                "current_price": 0.0
            }

        # 임시: 더미 지표 값 생성 (technical_indicators 모듈 없음)
        current_price = candles[-1]["close"] if candles else 0.0
        dummy_rsi = random.uniform(10, 90)
        dummy_k = random.uniform(0, 100)
        dummy_d = random.uniform(0, 100)

        # 더미 신호 생성 로직
        if dummy_k > dummy_d and dummy_rsi > rsi_long_threshold:
            signal = "long"
        elif dummy_k < dummy_d and dummy_rsi < rsi_short_threshold:
            signal = "short"
        else:
            signal = "wait"

        logging.warning(f"더미 함수 사용 - technical_indicators 모듈 없음")
        logging.info(f"[더미] 조건부 진입 지표 분석:")
        logging.info(f"  현재가: {current_price:.6f}")
        logging.info(f"  RSI(14): {dummy_rsi:.2f}")
        logging.info(f"  Stoch RSI K: {dummy_k:.2f}")
        logging.info(f"  Stoch RSI D: {dummy_d:.2f}")
        logging.info(f"  조건 확인:")
        logging.info(f"    K > D: {dummy_k > dummy_d} ({dummy_k:.2f} > {dummy_d:.2f})")
        logging.info(f"    RSI > 20: {dummy_rsi > rsi_long_threshold} ({dummy_rsi:.2f} > {rsi_long_threshold})")
        logging.info(f"    RSI < 80: {dummy_rsi < rsi_short_threshold} ({dummy_rsi:.2f} < {rsi_short_threshold})")
        logging.info(f"  최종 신호: {signal}")

        return {
            "signal": signal,
            "rsi": dummy_rsi,
            "stoch_rsi_k": dummy_k,
            "stoch_rsi_d": dummy_d,
            "current_price": current_price
        }
r = redis.Redis(host='localhost', port=6379, db=1, decode_responses=True)

# ✅ 웹소켓 기반 주문 상태 관리 함수들
def get_order_status(username: str, symbol: str) -> dict:
    """Redis에서 주문 상태 조회"""
    try:
        key = f"order_status:{username}:{symbol}"
        data = r.get(key)
        if data:
            return json.loads(data)
        return {}
    except Exception as e:
        return {}

def get_order_status_for_ui(username: str, symbol: str) -> list:
    """UI용 주문 상태 변환 (기존 format 호환)"""
    try:
        orders = get_order_status(username, symbol)
        result = []

        for price_str, status in orders.items():
            price = float(price_str)
            filled = status.get("filled", False)
            partial_sold = status.get("partial_sold", False)

            result.append({
                "price": price,
                "filled": filled,
                "partial_sold": partial_sold
            })

        # 가격순 정렬
        result.sort(key=lambda x: x["price"])
        return result
    except Exception as e:
        return []

# 사용자 설정 JSON 파일 저장 디렉토리
SETTINGS_DIR = "settings"
SERVER_URL = "http://43.201.67.246:8000"
_symbol_filter_store = {}
loggers = {}
TP_RECORD_DIR = "tp_records"
def get_user_logger(username: str) -> logging.Logger:
    """
    사용자 전용 Logger 생성 및 반환 (이미 있으면 캐시 사용).
    """
    if username in loggers:
        return loggers[username]

    logger = logging.getLogger(f"strategy.{username}")
    logger.setLevel(logging.DEBUG)  # DEBUG 레벨로 변경하여 상세 오류 정보 표시

    os.makedirs("logs", exist_ok=True)
    log_path = f"logs/{username}.log"

    # 핸들러 중복 방지
    if not logger.handlers:
        handler = RotatingFileHandler(log_path, maxBytes=5*1024*1024, backupCount=2)
        formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.propagate = False  # root 로거로 출력 방지

    loggers[username] = logger
    return logger

def round_price(value: float, digits: int) -> float:
    """주어진 소수 자릿수로 내림 처리합니다."""
    # ✅ 안전한 가격 반올림 (None 값 방지)
    if value is None or value <= 0:
        return 0.0
    try:
        factor = 10 ** digits
        return math.floor(value * factor) / factor
    except (TypeError, ValueError, OverflowError):
        return 0.0

def precision_digits(step_size: float) -> int:
    """step_size로부터 소수 자릿수를 계산합니다."""
    return int(-math.log10(step_size))

def adjust_qty(raw_qty: float, step_size: float) -> float:
    """수량을 stepSize 단위로 내림합니다."""
    return float((Decimal(str(raw_qty)) // Decimal(str(step_size))) * Decimal(str(step_size)))

def format_price(price: float, symbol: str = "") -> str:
    """가격을 적절한 소수점으로 포맷팅 (precision 오류 방지)"""
    if price >= 1:
        return f"{price:.4f}"  # 1 이상: 소수점 4자리
    elif price >= 0.1:
        return f"{price:.5f}"  # 0.1 이상: 소수점 5자리
    else:
        return f"{price:.6f}"  # 0.1 미만: 소수점 6자리

def get_candle_data(symbol: str) -> list:
    """coin 폴더에서 캔들 데이터 조회"""
    try:
        # 현재 스크립트와 같은 디렉토리의 coin 폴더에서 파일 찾기
        script_dir = os.path.dirname(os.path.abspath(__file__))
        coin_file = os.path.join(script_dir, "coin", f"{symbol}.json")

        if not os.path.exists(coin_file):
            return []

        with open(coin_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if "candle_data" not in data:
            return []

        # 4시간봉 데이터 조회
        candle_info = data["candle_data"].get("4h", {})
        return candle_info.get("candles", [])

    except Exception as e:
        logging.error(f"캔들 데이터 조회 실패 ({symbol}): {e}")
        return []

def check_conditional_entry(symbol: str, strategy_type: str, mode: str, logger=None) -> str:
    """조건부 진입 조건 확인"""
    try:
        # 조건부 진입이 아니면 기존 로직 사용
        if mode != "conditional":
            return mode

        # 캔들 데이터 조회
        candles = get_candle_data(symbol)
        if not candles:
            logging.warning(f"[{symbol}] 캔들 데이터 없음 - 대기")
            return "wait"

        logging.info(f"[{symbol}] 캔들 데이터 {len(candles)}개 조회됨")

        # 조건부 진입 분석
        analysis = analyze_conditional_entry(candles)
        signal = analysis["signal"]
        rsi = analysis.get("rsi", 0)
        stoch_k = analysis.get("stoch_rsi_k", 0)
        stoch_d = analysis.get("stoch_rsi_d", 0)
        current_price = analysis.get("current_price", 0)

        # 상세한 지표 값 로그 출력 (logger 사용)
        log_func = logger.info if logger else logging.info
        log_func(f"[{symbol}] 조건부 진입 지표 분석:")
        log_func(f"  현재가: {current_price:.6f}")
        log_func(f"  RSI(14): {rsi:.2f}")
        log_func(f"  Stoch RSI K: {stoch_k:.2f}")
        log_func(f"  Stoch RSI D: {stoch_d:.2f}")
        log_func(f"  조건 확인:")
        log_func(f"    K > D: {stoch_k > stoch_d} ({stoch_k:.2f} > {stoch_d:.2f})")
        log_func(f"    RSI > 20: {rsi > 20} ({rsi:.2f} > 20)")
        log_func(f"    RSI < 80: {rsi < 80} ({rsi:.2f} < 80)")
        log_func(f"  최종 신호: {signal}")

        # 전략별 신호 필터링
        # 전략별 신호 필터링 및 로그 출력
        log_func = logger.info if logger else logging.info

        if strategy_type == "cycle":
            # 순환매매법: 롱 신호만 허용
            if signal == "long":
                log_func(f"[{symbol}] 순환매매 롱 진입 신호")
                return "long"
            else:
                log_func(f"[{symbol}] 순환매매 대기 (신호: {signal})")
                return "wait"
        else:
            # 거미줄 매매법: 롱/숏 모두 허용
            if signal in ["long", "short"]:
                log_func(f"[{symbol}] 거미줄 매매 {signal} 진입 신호")
                return signal
            else:
                log_func(f"[{symbol}] 거미줄 매매 대기 (신호: {signal})")
                return "wait"

    except Exception as e:
        logging.error(f"조건부 진입 확인 실패 ({symbol}): {e}")
        return "wait"

def format_quantity(qty: float, symbol: str = "") -> str:
    """수량을 적절한 소수점으로 포맷팅 (precision 오류 방지)"""
    if qty >= 1:
        return f"{qty:.3f}"    # 1 이상: 소수점 3자리
    elif qty >= 0.1:
        return f"{qty:.4f}"    # 0.1 이상: 소수점 4자리
    else:
        return f"{qty:.5f}"    # 0.1 미만: 소수점 5자리

def get_symbol_filters_custom(client=None, symbol=None) -> dict:
    """
    coin 폴더의 JSON 파일에서 심볼 필터 정보를 읽어옴. 없으면 기본값 반환
    """
    try:
        import json
        import os

        if not symbol:
            # 심볼이 없으면 기본값 반환
            return {
                "minQty": 0.001,      # 기본 최소 수량
                "minNotional": 5,     # 기본 최소 금액 (USDT)
                "stepSize": 0.001,    # 기본 수량 단위
                "tickSize": 0.0001,   # 기본 가격 단위
                "minPrice": 0.0001,   # 기본 최소 가격
            }

        # coin 폴더의 심볼별 파일에서 필터 정보 로드
        coin_file = os.path.join("coin", f"{symbol}.json")

        if os.path.exists(coin_file):
            with open(coin_file, 'r', encoding='utf-8') as f:
                coin_data = json.load(f)

            # 필터 정보가 있으면 반환
            filters = coin_data.get("filters", {})
            if filters:
                logging.info(f"[{symbol}] 필터 정보 로드됨: stepSize={filters.get('stepSize')}, tickSize={filters.get('tickSize')}")
                return filters

        # 기존 symbol_filters.json 파일도 확인 (호환성)
        filters_file = "symbol_filters.json"
        if os.path.exists(filters_file):
            with open(filters_file, 'r', encoding='utf-8') as f:
                filters_data = json.load(f)

            # 해당 심볼의 필터 정보가 있으면 반환
            if symbol in filters_data:
                logging.info(f"[{symbol}] 기존 필터 파일에서 로드됨")
                return filters_data[symbol]

        # 없으면 기본값 반환
        logging.warning(f"[{symbol}] 필터 정보 없음 - 기본값 사용")
        return {
            "minQty": 0.001,      # 기본 최소 수량
            "minNotional": 5,     # 기본 최소 금액 (USDT)
            "stepSize": 0.001,    # 기본 수량 단위
            "tickSize": 0.0001,   # 기본 가격 단위
            "minPrice": 0.0001,   # 기본 최소 가격
        }
    except Exception as e:
        logging.error(f"필터 정보 로드 실패 ({symbol}): {e}")
        # 오류 시 기본값 반환
        return {
            "minQty": 0.001,
            "minNotional": 5,
            "stepSize": 0.001,
            "tickSize": 0.0001,
            "minPrice": 0.0001,
        }

async def get_symbol_price_http(symbol: str):
    try:
        resp = requests.get("http://localhost:8000/price", params={"symbol": symbol.upper()}, timeout=3)
        data = resp.json()
        return float(data["price"])
    except Exception as e:
        logging.warning(f"{symbol} 가격 조회 실패: {e}")
        return None
    
class TelegramNotifier:
    def __init__(self, bot_token, chat_id):
        self.bot_token = bot_token
        self.chat_id = chat_id

    def send_message(self, text):
        url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
        params = {'chat_id': self.chat_id, 'text': text}
        try:
            requests.get(url, params=params, timeout=3)
        except Exception as e:
            pass  # 텔레그램 알림 실패 로그 제거

TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_CHAT_ID = "-4761497604"
notifier = TelegramNotifier(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID)

class TradingManager:
    def __init__(self, username: str, api_key: str, api_secret: str):
        self.username = username
        self.client = Client(api_key, api_secret)
        self._running = False
        self._paused = False
        self.logger = get_user_logger(username)
        settings = self._load_user_config()
        self.strategy_type = settings.get("strategy_type", "grid")  # ← 이 위치로 옮기세요
        self.coin_settings = settings.get("coins", [])
        self.stop_on_profit = settings.get("stop_on_profit", False)
        # ✅ cycle_orders는 Redis에서 관리하므로 로컬 딕셔너리 제거
        # ❌ self.loss_tracker = {}  # Redis에서 실현손익 조회로 대체
        self.cycle_entry_usdt_map = {}
        self.usdt_per_layer_map = {}
        # ✅ 레버리지 설정 추적 (API 호출 최소화)
        self.leverage_set = set()
        self.logger.info(f"{username} 전략 인스턴스 초기화 완료.")
        self._shared_status = {
            "balance": 0.0,
            "positions": {},
            "open_limit_orders": {},
            "raw": {}
        }
        # ❌ tp_record 완전 제거 - 웹소켓 매니저에서 Redis로 모든 상태 추적
        # self.tp_record = defaultdict(dict)
        # os.makedirs(TP_RECORD_DIR, exist_ok=True)

    def get_position_realized_pnl(self, symbol: str) -> float:
        """Redis에서 포지션의 실현손익 조회 - 개선된 버전"""
        try:
            analysis_key = f"position_analysis:{self.username}"
            data_str = r.get(analysis_key)

            if data_str:
                analysis_data = json.loads(data_str)
                symbol_data = analysis_data.get(symbol, {})
                realized_pnl = symbol_data.get("realized_pnl", 0.0)
                return float(realized_pnl)
            else:
                # ✅ Redis에 데이터가 없으면 바이낸스 API에서 직접 조회
                try:
                    income_history = self.client.futures_income_history(symbol=symbol, incomeType="REALIZED_PNL", limit=50)
                    total_pnl = sum(float(item['income']) for item in income_history)
                    return total_pnl
                except Exception:
                    return 0.0

        except Exception as e:
            self.logger.error(f"실현손익 조회 실패 ({symbol}): {e}")
            return 0.0



    async def _status_updater(self):
        while self._running:
            try:
                status = get_user_status(self.username)
                self._shared_status["raw"] = status

                # Redis 디버깅 로그 제거

                # ✅ CoinPilot과 같은 방식으로 잔고 조회
                balances_raw = status.get("balances", {})
                if isinstance(balances_raw, list):
                    # 리스트면 dict로 변환
                    balances = {b["asset"]: b for b in balances_raw if "asset" in b}
                elif isinstance(balances_raw, dict):
                    balances = balances_raw
                else:
                    balances = {}

                # ✅ 문자열로 저장된 walletBalance를 안전하게 float로 변환
                usdt_data = balances.get("USDT", {})
                wallet_balance_str = usdt_data.get("walletBalance", "0")
                try:
                    wallet_balance = float(wallet_balance_str)
                except (ValueError, TypeError):
                    wallet_balance = 0.0

                self._shared_status["balance"] = wallet_balance

                # 잔고 변환 로그 제거

                self._shared_status["positions"] = (status.get("positions", {}))
                self._shared_status["open_limit_orders"] = status.get("open_limit_orders", {})
            except Exception as e:
                pass  # 상태 업데이트 실패 로그 제거
            await asyncio.sleep(0.5)
    #설정 불러오기
    def _load_user_config(self) -> dict:
        os.makedirs(SETTINGS_DIR, exist_ok=True)
        path = os.path.join(SETTINGS_DIR, f"{self.username}.json")

        if not os.path.exists(path):
            # ✅ 설정 파일이 없으면 기본 설정으로 생성
            # 설정 파일 없음 로그 제거
            try:
                default_config = {
                    "username": self.username,
                    "strategy_type": "grid",
                    "grid": {"coins": []},
                    "cycle": {"coins": []},
                    "max_martingale_layers": 7,
                    "stop_on_profit": False
                }
                with open(path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4, ensure_ascii=False)
                self.logger.info(f"[{self.username}] 기본 설정 파일 생성")
            except Exception as e:
                raise ValueError(f"[{self.username}] 설정 파일 생성 실패: {e}")

        try:
            with open(path) as f:
                cfg = json.load(f)

            # strategy_type 읽기
            strategy_type = cfg.get("strategy_type", "grid")

            # 해당 전략 타입 안에 coins 가져오기
            strategy_cfg = cfg.get(strategy_type, {})
            coins = strategy_cfg.get("coins", [])

            if coins:
                self.logger.info(f"[{self.username}] {strategy_type} 전략 시작: {len(coins)}개 코인")

            self.strategy_type = strategy_type
            return {
                "coins": coins,
                "strategy_type": strategy_type,
                "max_martingale_layers": cfg.get("max_martingale_layers", 7),
                "stop_on_profit": cfg.get("stop_on_profit", False)
            }

        except json.JSONDecodeError:
            raise ValueError(f"[{self.username}] 설정 파일이 손상되었습니다.")
        
    def _parse_binance_error(self, error_str: str) -> str:
        """바이낸스 오류 메시지를 사용자 친화적으로 변환"""
        error_str = str(error_str).lower()

        if "insufficient balance" in error_str or "account has insufficient balance" in error_str:
            return "잔고 부족"
        elif "min notional" in error_str or "notional" in error_str:
            return "최소 주문 금액 미달"
        elif "lot size" in error_str or "quantity" in error_str:
            return "주문 수량 오류"
        elif "price filter" in error_str:
            return "가격 범위 오류"
        elif "market is closed" in error_str:
            return "시장 마감"
        elif "symbol not found" in error_str:
            return "심볼 오류"
        elif "order would immediately match" in error_str:
            return "즉시 체결 오류"
        elif "timeout" in error_str:
            return "연결 시간 초과"
        elif "rate limit" in error_str:
            return "요청 한도 초과"
        else:
            # 원본 메시지가 너무 길면 줄임
            if len(error_str) > 100:
                return error_str[:100] + "..."
            return error_str

    async def send_error_report(self, symbol: str, message: str, stack_trace: str = None):
        url = "http://localhost:8000/report_error"
        import time
        payload = {
            "username": self.username,
            "symbol": symbol,
            "message": message,
            "timestamp": time.time()
        }

        # 스택트레이스가 제공되면 추가
        if stack_trace:
            payload["stack_trace"] = stack_trace
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, timeout=2) as resp:
                    if resp.status != 200:
                        self.logger.warning(f"[{self.username}] {symbol} 오류 보고 실패 (상태 코드 {resp.status})")
        except Exception as e:
            self.logger.warning(f"[{self.username}] {symbol} 오류 보고 예외: {e}")

    async def send_success_report(self, symbol: str, message: str):
        """성공 메시지는 로그로만 기록 (CoinPilot에 오류로 전송하지 않음)"""
        self.logger.info(f"[{self.username}] {symbol} 성공: {message}")

    async def send_clear_report(self):
        url = "http://localhost:8000/clear_error"
        payload = {
            "username": self.username
        }
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, timeout=2) as resp:
                    if resp.status != 200:
                        self.logger.warning(f"[{self.username}]오류 초기화 실패 (상태 코드 {resp.status})")
        except Exception as e:
            self.logger.warning(f"[{self.username}] 오류 초기화 예외: {e}")        
    
    def _load_tp_record(self):
        # ✅ TP 기록 로직 개선 - Redis 기반으로 전환하되 기존 상태 보존
        try:
            legacy_path = os.path.join(TP_RECORD_DIR, f"{self.username}_cycle_tp.json")
            if os.path.exists(legacy_path):
                # ✅ 기존 파일이 있으면 Redis로 마이그레이션 후 삭제
                try:
                    with open(legacy_path, 'r') as f:
                        legacy_data = json.load(f)

                    # Redis에 마이그레이션
                    if legacy_data:
                        migration_key = f"tp_migration:{self.username}"
                        r.set(migration_key, json.dumps(legacy_data), ex=86400)  # 24시간 보관
                        self.logger.info(f"[{self.username}] 레거시 TP 데이터 Redis로 마이그레이션 완료")

                    os.remove(legacy_path)
                    self.logger.info(f"[{self.username}] 레거시 TP 파일 삭제 완료")
                except json.JSONDecodeError:
                    self.logger.warning(f"[{self.username}] 레거시 TP 파일 손상됨, 삭제만 진행")
                    os.remove(legacy_path)

            self.logger.info(f"[{self.username}] Redis 기반 거래 상태 추적 시작")
        except Exception as e:
            self.logger.warning(f"[{self.username}] TP 레코드 초기화 실패: {e}")

    # ❌ TP 관련 함수들 완전 제거 - 웹소켓 매니저에서 Redis로 대체
    # def _save_tp_record(self): pass
    # def update_tp_state(self, symbol, price, filled=None, tp_hit=None): pass
    # def init_tp_state(self, symbol, prices: list[float]): pass
    # def clear_tp_state(self, symbol): pass



    async def run(self):
        if self._running:
            self.logger.info(f"[{self.username}] 이미 실행 중인 전략입니다.")
            return
        self._running = True
        self.set_coin = {}
        self.logger.info(f"[{self.username}] 전략 실행 시작.")
        asyncio.create_task(self._status_updater())
        self.logger.info(f"[{self.username}] task 생성 완료, 변수 초기화 시작.")
        reference_prices = {}
        positions = {}
        entry_levels_long = {}
        entry_levels_short = {}

        # ✅ strategy_type 정의 (안전한 방법으로 수정)
        try:
            strategy_type = self.config.get("strategy_type", "grid")
        except AttributeError:
            # self.config가 없는 경우 직접 로드
            config = self._load_user_config()
            strategy_type = config.get("strategy_type", "grid")
            self.config = config
        for c in self.coin_settings:
            s = c["symbol"].upper()
            reference_prices[s] = None
            entry_levels_long[s] = 0
            entry_levels_short[s] = 0
        try:
            try:
                # ✅ 전략 시작 시 오류 메시지 초기화
                await self.send_clear_report()
                self.logger.info(f"[{self.username}] 전략 시작으로 인한 오류 메시지 초기화")
            except Exception as e:
                self.logger.error(f"[{self.username}] 오류 초기화 실패: {e}")

            try:
                self._load_tp_record()
            except Exception as e:
                self.logger.error(f"[{self.username}] TP 레코드 로딩 실패: {e}")

            try:
                from websocket_manager import ensure_symbol_stream, handle_symbol_stream, set_global_session,session
            except Exception as e:
                self.logger.error(f"[{self.username}] 웹소켓 매니저 임포트 실패: {e}")

            try:
                if session is None:
                    sess = aiohttp.ClientSession()
                    set_global_session(sess)
                    for coin in self.coin_settings:
                        symbol = coin["symbol"].upper()
                        await ensure_symbol_stream(symbol, sess, handle_symbol_stream)
            except Exception as e:
                self.logger.error(f"[{self.username}] 웹소켓 세션 설정 실패: {e}")
            while self._running:
                # USDT 잔고 조회 (직접 Redis에서 가져오기)
                try:
                    # ✅ 동기화 문제 해결: 직접 get_user_status() 호출
                    status = get_user_status(self.username)
                    balances_raw = status.get("balances", {})

                    if isinstance(balances_raw, list):
                        balances = {b["asset"]: b for b in balances_raw if "asset" in b}
                    elif isinstance(balances_raw, dict):
                        balances = balances_raw
                    else:
                        balances = {}

                    usdt_data = balances.get("USDT", {})
                    wallet_balance_str = usdt_data.get("walletBalance", "0")
                    balance = float(wallet_balance_str)

                    # 메인루프 잔고 로그 제거

                except Exception as e:
                    balance = 0.0
                    self.logger.error(f"[{self.username}] 잔고 조회 실패: {e}")
                interval = 1.0
                for coin in self.coin_settings:
                    sym = coin["symbol"].upper()

                    # 메인 루프 진행 로그 제거
                    stop_flag = await asyncio.to_thread(r.get, f"stop_after_profit:{self.username}")
                    if str(stop_flag).strip().lower() in ("1", "true") and not self.stop_on_profit:
                        self.stop_on_profit = True
                        self.logger.info(f"[{self.username}] 익절 후 종료 모드 진입")

                    # ✅ 이익 후 중지 모드에서 매 루프마다 모든 포지션 체크
                    if self.stop_on_profit:
                        all_flat = True
                        for check_coin in self.coin_settings:
                            check_sym = check_coin["symbol"].upper()
                            try:
                                positions = self._shared_status.get("positions", {})
                                amt = float(positions.get(check_sym, {}).get("positionAmt", 0))
                                if abs(amt) > 0.0001:
                                    all_flat = False
                                    break
                            except Exception:
                                all_flat = False
                                break

                        if all_flat:
                            self.logger.info(f"[{self.username}] 이익 후 중지: 모든 포지션 없음 → 전략 종료")
                            self._running = False
                            break
                    cached_positions = self._shared_status.get("positions", {})
                    sym_info = cached_positions.get(sym, {})
                    position_amt = float(sym_info.get("positionAmt", 0))
                    entry_price = float(sym_info.get("entryPrice", 0))

                    # 포지션 체크 로그 제거
                    # ✅ 기존 포지션 인식 로직 (모든 전략에 대해 실행)
                    if position_amt != 0:
                        # 포지션이 존재할 경우: entry_levels 설정
                        reference_prices[sym] = entry_price
                        # 포지션 존재 확인 로그 제거

                        # ✅ 재시작 시 기존 포지션 인식 로그 (최초 1회만)
                        if not hasattr(self, '_position_recognized') or sym not in getattr(self, '_position_recognized', set()):
                            if not hasattr(self, '_position_recognized'):
                                self._position_recognized = set()
                            self._position_recognized.add(sym)

                            if strategy_type == "cycle":
                                order_status = get_order_status(self.username, sym)
                                if order_status:
                                    filled_count = sum(1 for _, status in order_status.items() if status.get("filled", False))
                                    entry_levels_long[sym] = filled_count
                                    self.logger.info(f"[{self.username}] {sym} 기존 순환매매 포지션 인식: {position_amt:.4f}개, {filled_count}개 레이어 체결됨")
                                else:
                                    entry_levels_long[sym] = 1
                                    self.logger.info(f"[{self.username}] {sym} 기존 순환매매 포지션 인식: {position_amt:.4f}개")
                            else:
                                # 그리드 매매
                                entry_levels_long[sym] = 1
                                entry_levels_short[sym] = 1
                                if position_amt > 0:
                                    self.logger.info(f"[{self.username}] {sym} 기존 롱 포지션 인식: {position_amt:.4f}개 @ {entry_price:.4f}")
                                else:
                                    self.logger.info(f"[{self.username}] {sym} 기존 숏 포지션 인식: {abs(position_amt):.4f}개 @ {entry_price:.4f}")

                        # ✅ 레버리지 설정 (최초 1회만)
                        if sym not in self.leverage_set:
                            for coin in self.coin_settings:
                                if coin["symbol"].upper() == sym:
                                    lev = coin.get("leverage", 10)
                                    try:
                                        self.client.futures_change_leverage(symbol=sym, leverage=lev)
                                        self.leverage_set.add(sym)
                                    except Exception:
                                        pass
                                    break

                    # ✅ Redis에서 주문 상태 확인
                    order_status = get_order_status(self.username, sym)
                    if order_status:  # 주문 상태가 있으면 순환매매 진행 중
                        pos_amt = float(self._shared_status.get("positions", {}).get(sym, {}).get("positionAmt", 0))
                        if abs(pos_amt) < 0.0001:
                            # 포지션 없음 로그 제거
                            # ✅ 포지션 없을 때 모든 주문 취소 후 cycle_orders 정리
                            try:
                                self.client.futures_cancel_all_open_orders(symbol=sym)
                                pass  # 주문 취소 로그 제거
                            except Exception:
                                pass

                            # ✅ Redis에서 주문 상태 정리 (웹소켓에서 자동 처리됨)
                            pass  # 웹소켓 매니저에서 포지션 변화 감지 시 자동 초기화
                            # 🔧 재진입을 위해 set_coin 리셋
                            self.set_coin[sym] = False
                            # 🔧 기준가도 리셋하여 새로운 진입 허용
                            reference_prices[sym] = None

                        # 중복된 포지션 인식 로직 제거됨 (위에서 이미 처리)

                    # ✅ 포지션 상태에 따른 처리 로직 개선 (올바른 들여쓰기로 이동)
                    if position_amt == 0:
                        # 포지션이 없는 경우
                        if not self.set_coin.get(sym, False):
                            # 최초 진입 또는 재진입 준비
                            self.set_coin[sym] = True
                            reference_prices[sym] = None  # 기준가 리셋하여 즉시 진입 가능
                        # 진입 준비 로그 제거
                    else:
                        # 포지션이 있는 경우
                        self.set_coin[sym] = True

                    # 🔧 현재 심볼에 맞는 코인 설정 찾기 (모든 경우)
                    current_coin = None
                    for coin_config in self.coin_settings:
                        if coin_config["symbol"].upper() == sym:
                            current_coin = coin_config
                            break

                    if current_coin is None:
                        continue

                    frac = current_coin.get("fraction", 10.0)
                    # ✅ fraction 안전성 검증
                    if frac is None or frac <= 0:
                        frac = 10.0  # 기본값 10%
                    fraction = frac/100.0 if frac>1 else frac

                    lev = current_coin.get("leverage", 10)
                    # ✅ leverage 안전성 검증
                    if lev is None or lev <= 0:
                        lev = 10  # 기본값 10배

                    mults = current_coin.get("custom_multipliers", [])
                    # ✅ multipliers 안전성 검증
                    if not mults:
                        self.logger.warning(f"[{self.username}] {sym} 배수 설정이 없어 건너뜀")
                        continue
                    tp_pct = current_coin.get("take_profit_pct", 2.0)
                    # ✅ tp_pct 안전성 검증
                    if tp_pct is None or tp_pct <= 0:
                        tp_pct = 2.0  # 기본값 설정
                    mode = current_coin.get("mode", "both")
                    # ✅ mode 안전성 검증
                    if mode not in ["long", "short", "both", "conditional"]:
                        mode = "both"  # 기본값

                    entry_threshold_pct = current_coin.get("entry_threshold_pct", 0) if self.strategy_type == "grid" else 0
                    # ✅ entry_threshold_pct 안전성 검증
                    if entry_threshold_pct is None:
                        entry_threshold_pct = 0

                    # 조건부 진입 모드에서는 전략 타입에 따라 allow_long/short 설정
                    if mode == "conditional":
                        if strategy_type == "cycle":
                            # 순환매매: 롱만 허용
                            allow_long = True
                            allow_short = False
                        else:
                            # 거미줄 매매: 롱/숏 모두 허용
                            allow_long = True
                            allow_short = True
                    else:
                        # 기존 로직
                        allow_long = mode in ("both","long")
                        allow_short= mode in ("both","short")

                    # 포지션 상태 체크 로그 제거

                    # 🔧 주문 취소 로직은 포지션이 있는 경우에만 실행
                    if position_amt != 0:
                        status = get_user_status(self.username)
                        orders = status.get("orders", [])
                        latest_order_times = {}
                        for order in orders:
                                symbol = order.get("symbol")
                                update_time = order.get("updateTime") or order.get("time")
                                if symbol and update_time:
                                    prev = latest_order_times.get(symbol, 0)
                                    latest_order_times[symbol] = max(prev, update_time)
                        open_limit_orders = status.get("open_limit_orders", {})
                        positions = status.get("positions", {})
                        symbols_with_position = {
                            symbol_name for symbol_name, data in positions.items()  # ✅ 변수명 충돌 해결
                            if abs(float(data.get("positionAmt", 0))) > 0.0001
                        }
                        # 🔧 open_limit_orders 데이터 구조 처리 (리스트 또는 숫자)
                        symbols_with_limit_orders = set()
                        for symbol_name, orders_data in open_limit_orders.items():  # ✅ 변수명 충돌 해결
                                if isinstance(orders_data, list):
                                    # 새로운 형태: {symbol: [order_list]}
                                    if len(orders_data) > 0:
                                        symbols_with_limit_orders.add(symbol_name)
                                elif isinstance(orders_data, (int, float)):
                                    # 기존 형태: {symbol: count}
                                    if orders_data > 0:
                                        symbols_with_limit_orders.add(symbol_name)
                        symbols_with_only_limit_orders = symbols_with_limit_orders - symbols_with_position
                        for all_symbol in symbols_with_only_limit_orders:
                            try:
                                self.client.futures_cancel_all_open_orders(symbol=all_symbol)
                            except Exception:
                                pass

                    # 현재가 조회 - 개선된 오류 처리
                    # 가격 조회 시작 로그 제거
                    price = None
                    retry_count = 0
                    max_retries = 3

                    while retry_count < max_retries and price is None:
                        try:
                            price = await get_price(sym)
                            if price and float(price) > 0:
                                break
                            else:
                                price = None
                        except Exception:
                            price = None

                        retry_count += 1
                        if retry_count < max_retries:
                            await asyncio.sleep(1)  # 1초 대기 후 재시도

                    if price is None:
                        continue

                    # 조건부 진입 신호 초기화
                    entry_signal = "none"

                    # 기준가 설정
                    if reference_prices[sym] is None:
                        reference_prices[sym] = price
                        # 기준가 설정 로그 제거

                    ref = reference_prices[sym]
                    try:
                        # ✅ ref가 None이거나 0인 경우 안전하게 처리
                        if ref is None or ref == 0:
                            change = 0
                        else:
                            change = (price-ref)/ref*100
                    except (ZeroDivisionError, TypeError):
                        # 나누기 오류 및 None 타입 오류 처리
                        change = 0

                    # 진입 허용 조건 검사
                    strategy_type = self.strategy_type  # <-- run_strategy.py 초기 설정에서 추가돼 있어야 함
                    positions = self._shared_status.get("positions", {})
                    symbols_with_position = {
                        sym for sym, data in positions.items()
                        if abs(float(data.get("positionAmt", 0))) > 0.0001
                    }
                    # 진입 조건 확인
                    self.logger.info(f"[{sym}] 진입 조건 체크:")
                    self.logger.info(f"  - 포지션 있음: {sym in symbols_with_position}")
                    self.logger.info(f"  - 롱 진입 레벨: {entry_levels_long[sym]}")
                    self.logger.info(f"  - 숏 진입 레벨: {entry_levels_short[sym]}")

                    if sym not in symbols_with_position and entry_levels_long[sym] == 0 and entry_levels_short[sym] == 0:
                            self.logger.info(f"[{sym}] 진입 조건 확인 - 포지션 없음, 진입 레벨 0")
                            # ✅ 이익 후 중지 모드에서는 새로운 진입 금지
                            if self.stop_on_profit:
                                allow_entry = False
                                self.logger.info(f"[{sym}] 수익 후 중지로 인한 진입 차단")
                            else:
                                entry_threshold = entry_threshold_pct

                                # 조건부 진입 확인
                                coin_config = next((c for c in self.coin_settings if c["symbol"].upper() == sym), {})
                                coin_mode = coin_config.get("mode", "both")  # position_mode → mode로 수정
                                self.logger.info(f"[{sym}] 코인 설정: {coin_config}")
                                self.logger.info(f"[{sym}] 코인 모드 확인: {coin_mode} (전략: {strategy_type})")

                                if coin_mode == "conditional":
                                    # 조건부 진입 모드
                                    self.logger.info(f"[{sym}] 조건부 진입 확인 시작 (모드: {coin_mode})")
                                    entry_signal = check_conditional_entry(sym, strategy_type, coin_mode, self.logger)

                                    if entry_signal == "wait":
                                        allow_entry = False
                                        self.logger.info(f"[{sym}] 조건부 진입 대기")
                                    elif entry_signal == "long" and allow_long:
                                        allow_entry = True
                                        self.logger.info(f"[{sym}] 조건부 진입 롱 허용")
                                    elif entry_signal == "short" and allow_short:
                                        allow_entry = True
                                        self.logger.info(f"[{sym}] 조건부 진입 숏 허용")
                                    else:
                                        allow_entry = False
                                        self.logger.info(f"[{sym}] 조건부 진입 거부: 신호={entry_signal}, allow_long={allow_long}, allow_short={allow_short}")
                                else:
                                    # 기존 로직 (조건부 진입이 아닌 경우)
                                    if strategy_type == "cycle":
                                        # 순환매매는 즉시 진입
                                        allow_entry = True
                                    elif entry_threshold == 0:
                                        # 진입 변동성 0이면 무조건 진입
                                        allow_entry = True
                                    elif (change >= entry_threshold and allow_long) or (change <= -entry_threshold and allow_short):
                                        # 변동성 기반 진입 로직
                                        allow_entry = True
                                    else:
                                        allow_entry = False

                            if not allow_entry:
                                await asyncio.sleep(interval)
                                continue
                    # 기준 주문 금액 계산
                    nl = len(mults)
                    total_mul = sum(mults[:nl])
                    # ✅ 안전한 나눗셈을 위한 검증
                    if balance is None or balance <= 0:
                        self.logger.warning(f"[{self.username}] {sym} 잔고 오류: {balance}")
                        continue            
                    if total_mul is None or total_mul <= 0:
                        self.logger.warning(f"[{self.username}] {sym} 배수 합계 오류: {total_mul}")
                        continue      
                    base_amt = balance * fraction / total_mul
                    # 매매 방향 결정
                    direction = None
                    positions = self._shared_status.get("positions", {})
                    position_amt = float(positions.get(sym, {}).get("positionAmt", 0))
                    # position_amt 디버깅 로그 제거
                    if abs(position_amt) < 0.0001:
                        # 조건부 진입 신호에 따른 방향 결정
                        if entry_signal == "long":
                            direction = 'long'
                        elif entry_signal == "short":
                            direction = 'short'
                        elif mode == 'long' or (change > 0 and allow_long):
                            direction = 'long'
                        elif mode == 'short' or (change < 0 and allow_short):
                            direction = 'short'

                    # 필터 정보 및 소수점 자리수 계산
                    flt = get_symbol_filters_custom(self.client, sym)
                    precision = precision_digits(flt["tickSize"])
                    step_size = flt["stepSize"]
                    min_notl = flt["minNotional"]
                    min_qty = flt["minQty"]

                    # 디버깅: 필터 정보 로그
                    logging.info(f"[{sym}] 필터 정보: stepSize={step_size}, tickSize={flt['tickSize']}, minQty={min_qty}, minNotional={min_notl}, precision={precision}")
                    # "1" 로그 제거

                    # ✅ 순환매매 진입 로직 (완전히 새로 구현)
                    if entry_levels_long[sym] == 0 and strategy_type == "cycle":
                        await self.execute_cycle_entry(sym, current_coin, price, balance, fraction, lev, flt, precision, reference_prices, entry_levels_long)

                    if direction=='long' and entry_levels_long[sym]==0 and strategy_type == "grid":
                        # 시장가 레이어0 진입
                        # ✅ 안전한 나눗셈을 위한 검증
                        if price is None or price <= 0:
                            self.logger.warning(f"[{self.username}] {sym} 가격 오류: {price}")
                            continue

                        val0 = base_amt * mults[0] * lev
                        # ✅ 안전한 나눗셈을 위한 검증
                        if price is None or price <= 0:
                            self.logger.warning(f"[{self.username}] {sym} 가격 오류로 진입 실패: {price}")
                            continue
                        raw0 = val0 / price
                        q0 = adjust_qty(raw0, step_size)

                        if q0 < min_qty and raw0 >= min_qty:
                            q0 = min_qty

                        if (q0 * price) < min_notl:
                            await self.send_error_report(sym, "주문량이 최소 수량보다 작음")
                            continue

                        if q0 > 0 and price * q0 >= min_notl:
                            try:
                                self.client.futures_create_order(
                                    symbol=sym, side="BUY", type="MARKET", quantity=format_quantity(q0, sym))
                                self.logger.info(f"[{self.username}] {sym} 매수 진입: {q0}")
                                # ✅ 포지션 상태는 websocket에서 업데이트되므로 로컬 수정 제거
                                # positions[sym] = {"positionAmt": str(q0), "entryPrice": str(price)}
                                
                                entry_levels_long[sym] = 1
                                try:
                                    await self.send_success_report(sym, "그리드 매수 진입 완료")
                                except Exception as e:
                                    pass
                                
                                # 물타기 지정가 레이어(1 이상)
                                custom_levels = current_coin.get("custom_levels", [])
                                custom_multipliers = current_coin.get("custom_multipliers", [])

                                # ✅ 커스텀 설정값 안전성 검증
                                if not custom_levels or not custom_multipliers:
                                    self.logger.warning(f"[{self.username}] {sym} 커스텀 레벨/배수 설정이 없어 매수 건너뜀")
                                    continue

                                total_multiplier = sum(custom_multipliers[:nl])  # 실제 사용될 레벨 수만큼 합산

                                # ✅ 지정가 주문 카운터
                                limit_order_count = 0

                                for lv in range(1, nl):
                                    
                                    if lv >= len(custom_levels) or lv >= len(custom_multipliers):
                                        break

                                    level_pct = sum(custom_levels[1:lv + 1])  # 누적 거리 계산
                                    # ✅ 가격 유효성 검증
                                    if price is None or price <= 0:
                                        break
                                    pr = round_price(price * (1 - level_pct / 100), precision)
                                    if pr <= 0:
                                        break
                                        
                                    # ✅ 안전한 나눗셈을 위한 검증
                                    if total_multiplier is None or total_multiplier <= 0:
                                        break
                                    if pr is None or pr <= 0:
                                        break

                                    weight = custom_multipliers[lv] / total_multiplier
                                    usdt_amount = balance * fraction * weight
                                    val = usdt_amount * lev
                                    # ✅ 안전한 나눗셈을 위한 검증
                                    if pr is None or pr <= 0:
                                        continue
                                    q = adjust_qty(val / pr, step_size)
                                   
                                    if q > 0 and pr * q >= min_notl:
                                        self.client.futures_create_order(
                                            symbol=sym, side="BUY", type="LIMIT",
                                            timeInForce="GTC", price=format_price(pr, sym), quantity=format_quantity(q, sym))
                                        limit_order_count += 1

                                # ✅ 지정가 주문 요약 로그
                                if limit_order_count > 0:
                                    self.logger.info(f"[{self.username}] {sym} 지정가 매수 {limit_order_count}개 주문 완료")

                                await asyncio.sleep(0.5)
                                positions = self._shared_status.get("positions", {})
                                entry_levels_long[sym] = nl

                            except Exception as e:
                                error_msg = self._parse_binance_error(str(e))
                                tb = traceback.format_exc()
                                self.logger.info(f"[{self.username}] {sym} 매수 실패: {error_msg}")
                                self.logger.debug(f"[{self.username}] {sym} 매수 실패 상세:\n{tb}")
                                await self.send_error_report(sym, error_msg, tb)

                    elif direction=='short' and entry_levels_short[sym]==0 and strategy_type == "grid":
                        # 시장가 레이어0 진입
                        val0 = base_amt * mults[0] * lev
                        # ✅ 안전한 나눗셈을 위한 검증
                        if price is None or price <= 0:
                            self.logger.warning(f"[{self.username}] {sym} 가격 오류로 숏 진입 실패: {price}")
                            continue
                        raw0 = val0 / price
                        q0 = adjust_qty(raw0, step_size)
                        if q0 < min_qty and raw0 >= min_qty:
                            q0 = min_qty

                        if (q0 * price) < min_notl:
                            await self.send_error_report(sym, "주문 금액이 최소 주문 금액보다 작음")
                            continue

                        if q0 > 0 and price * q0 >= min_notl:
                            try:
                                self.client.futures_create_order(
                                    symbol=sym, side="SELL", type="MARKET", quantity=format_quantity(q0, sym))
                                self.logger.info(f"[{self.username}] {sym} 매도 진입: {q0}")
                                # ✅ 포지션 상태는 websocket에서 업데이트되므로 로컬 수정 제거
                                # positions[sym] = {"positionAmt": str(-q0), "entryPrice": str(price)}
                                entry_levels_short[sym] = 1
                                try:
                                    await self.send_success_report(sym, "그리드 매도 진입 완료")
                                except Exception as e:
                                    pass
                                # 물타기 지정가 레이어(1 이상) - 숏 포지션도 custom_levels 사용
                                custom_levels = current_coin.get("custom_levels", [])
                                custom_multipliers = current_coin.get("custom_multipliers", [])

                                # ✅ 커스텀 설정값 안전성 검증
                                if not custom_levels or not custom_multipliers:
                                    self.logger.warning(f"[{self.username}] {sym} 커스텀 레벨/배수 설정이 없어 매도 건너뜀")
                                    continue

                                total_multiplier = sum(custom_multipliers[:nl])  # 실제 사용될 레벨 수만큼 합산

                                # ✅ 지정가 주문 카운터
                                limit_order_count = 0

                                for lv in range(1, nl):
                                    if lv >= len(custom_levels) or lv >= len(custom_multipliers):
                                        break

                                    level_pct = sum(custom_levels[1:lv + 1])  # 누적 거리 계산
                                    # ✅ 가격 유효성 검증
                                    if price is None or price <= 0:
                                        break
                                    # 숏 포지션: 현재가에서 위쪽으로 지정가 매도
                                    pr = round_price(price * (1 + level_pct / 100), precision)
                                    # ✅ 안전한 나눗셈을 위한 검증
                                    if pr is None or pr <= 0:
                                        continue

                                    # ✅ 안전한 나눗셈을 위한 검증
                                    if total_multiplier is None or total_multiplier <= 0:
                                        break

                                    weight = custom_multipliers[lv] / total_multiplier
                                    usdt_amount = balance * fraction * weight
                                    val = usdt_amount * lev
                                    q = adjust_qty(val / pr, step_size)
                                    if q > 0 and pr * q >= min_notl:
                                        self.client.futures_create_order(
                                            symbol=sym, side="SELL", type="LIMIT",
                                            timeInForce="GTC", price=format_price(pr, sym), quantity=format_quantity(q, sym))
                                        limit_order_count += 1

                                # ✅ 지정가 주문 요약 로그
                                if limit_order_count > 0:
                                    self.logger.info(f"[{self.username}] {sym} 지정가 매도 {limit_order_count}개 주문 완료")

                                await asyncio.sleep(0.5)
                                positions = self._shared_status.get("positions", {})
                                entry_levels_short[sym] = nl
                            except Exception as e:
                                error_msg = self._parse_binance_error(str(e))
                                tb = traceback.format_exc()
                                self.logger.info(f"[{self.username}] {sym} 매도 실패: {error_msg}")
                                self.logger.debug(f"[{self.username}] {sym} 매도 실패 상세:\n{tb}")
                                await self.send_error_report(sym, error_msg, tb)

                    # 익절 및 포지션 청산 처리 (직접 Redis에서 조회)
                    status = get_user_status(self.username)
                    positions = status.get("positions", {})

                    # 익절 로직 진입 디버깅 제거

                    if positions.get(sym):
                        sym_info = positions.get(sym, {})
                        # ✅ 안전한 타입 변환
                        try:
                            position_amt = float(sym_info.get("positionAmt", 0) or 0)
                            entry_price = float(sym_info.get("entryPrice", 0) or 0)
                        except (ValueError, TypeError) as e:
                            pass  # 포지션 데이터 타입 오류 로그 제거
                            position_amt = 0.0
                            entry_price = 0.0

                        if abs(position_amt) < 0.0001:
                            continue

                        # 평단 기준 익절 가격 계산
                        if strategy_type == "cycle":
                            partial_tp_pct = coin.get("cycle_partial_tp_pct", 2.0)
                            final_tp_pct = coin.get("cycle_final_tp_pct", 4.0)

                            # ✅ 익절 설정값 안전성 검증
                            if partial_tp_pct is None or partial_tp_pct <= 0:
                                partial_tp_pct = 2.0
                            if final_tp_pct is None or final_tp_pct <= 0:
                                final_tp_pct = 4.0

                            # ✅ Redis에서 주문 상태 조회 (websocket_manager에서 실시간 업데이트됨)
                            order_status = get_order_status(self.username, sym)

                            # 지정가 체결된 레벨 중 아직 익절 안한 레벨 찾기
                            filled_layers = sorted([
                                    float(price) for price, status in order_status.items()
                                    if status.get("filled", False) and not status.get("partial_sold", False)
                                ])
                            if len(filled_layers) >= 2:
                                    for i in range(1, len(filled_layers)):  # 두 번째 레이어부터 시작
                                        layer_price = filled_layers[i]

                                        # ✅ 가격 유효성 검증
                                        if layer_price is None or layer_price <= 0:
                                            continue
                                        trigger_price = layer_price * (1 + partial_tp_pct / 100)
                                        if price >= trigger_price:
                                            # ✅ 안전한 나눗셈을 위한 검증
                                            cycle_layer_count = coin.get("cycle_layer_count", 3)
                                            if cycle_layer_count <= 0:
                                                cycle_layer_count = 3

                                            usdt_per_layer = self.usdt_per_layer_map.get(sym, (balance * fraction) / cycle_layer_count)
                                            order_val = usdt_per_layer * lev

                                            if price is None or price <= 0:
                                                self.logger.warning(f"[{self.username}] {sym} 부분익절 가격 오류: {price}")
                                                continue

                                            raw_qty = order_val / price
                                            q_close = adjust_qty(raw_qty, step_size)

                                            if q_close >= flt["minQty"]:
                                                try:
                                                    self.client.futures_create_order(
                                                        symbol=sym, side="SELL", type="MARKET", quantity=format_quantity(q_close, sym))

                                                    # ✅ 부분매도는 웹소켓에서 자동 감지됨
                                                    pass  # 웹소켓 매니저에서 시장가 매도 감지 시 자동 업데이트
                                                    self.logger.info(f"[{self.username}] {sym} 부분 익절 완료")

                                                    # ✅ 추가 레이어 생성 로직 (기존 가격들 기준)
                                                    current_orders = get_order_status(self.username, sym)
                                                    existing_prices = sorted([float(p) for p in current_orders.keys()])
                                                    if existing_prices:
                                                        bottom_layer_price = min(existing_prices)
                                                        new_layer_pct = coin.get("cycle_bottom_layer_pct", 1.0) / (len(existing_prices) - 1 or 1)
                                                        # ✅ 가격 유효성 검증
                                                        if bottom_layer_price is None or bottom_layer_price <= 0:
                                                            continue
                                                        new_price = round_price(bottom_layer_price * (1 - new_layer_pct / 100), precision)

                                                        usdt_per_layer = self.usdt_per_layer_map.get(sym, (balance * fraction) / coin.get("cycle_layer_count", 3))
                                                        val = usdt_per_layer * lev
                                                        # ✅ 안전한 나눗셈을 위한 검증
                                                        if new_price is None or new_price <= 0:
                                                            continue
                                                        q = adjust_qty(val / new_price, step_size)

                                                        if q >= min_qty and new_price * q >= min_notl:
                                                            try:
                                                                self.client.futures_create_order(
                                                                    symbol=sym,
                                                                    side="BUY",
                                                                    type="LIMIT",
                                                                    price=format_price(new_price, sym),
                                                                    quantity=format_quantity(q, sym),
                                                                    timeInForce="GTC"
                                                                )
                                                                self.logger.info(f"[{self.username}] {sym} 추가 레이어: {new_price} x {q}")
                                                                # ✅ 새 레이어는 웹소켓에서 자동 추가됨
                                                                pass  # 웹소켓 매니저에서 지정가 주문 생성 시 자동 추가
                                                            except Exception as e:
                                                                pass
                                                except Exception:
                                                    pass

                            # ✅ 최종 익절 - 개선된 계산 로직
                            pnl_usdt = (price - entry_price) * position_amt if position_amt > 0 else (entry_price - price) * abs(position_amt)

                            # ✅ 현재 미실현 손익이 목표 수익률에 도달했는지 확인
                            expected_usdt = abs(entry_price * position_amt) * (final_tp_pct / 100)

                            # 🆕 Redis에서 실현손익 조회 (이전 손실 복구용)
                            realized_pnl = self.get_position_realized_pnl(sym)
                            recover_usdt = abs(realized_pnl) if realized_pnl < 0 else 0  # 손실만 복구 대상

                            positions = self._shared_status.get("positions", {})
                            # ✅ 현재 수익이 목표 수익 + 손실 복구액을 넘으면 익절
                            if pnl_usdt >= expected_usdt + recover_usdt and positions.get(sym):
                                q_close = adjust_qty(abs(position_amt), step_size)
                                try:
                                    lev = coin.get("leverage", 10)
                                    
                                    # ✅ 포지션 방향에 따른 올바른 side 설정
                                    if position_amt > 0:
                                        side = "SELL"  # 롱 포지션 종료
                                    else:
                                        side = "BUY"   # 숏 포지션 종료

                                    # ✅ 모든 경우에 올바른 side 사용
                                    self.client.futures_create_order(
                                        symbol=sym, side=side, type="MARKET", quantity=format_quantity(q_close, sym), reduceOnly=True)
                                    
                                    self.logger.info(f"[{self.username}] {sym} 최종 익절: +{pnl_usdt:.2f} USDT")
                                    await asyncio.sleep(1)
                                    notifier.send_message(f"[익절] {self.username} - {sym} 포지션 종료\n수익금: {pnl_usdt:.2f} USDT\n잔고: {self._shared_status['balance']:.2f} USDT")
                                    self.client.futures_cancel_all_open_orders(symbol=sym)
                                    # ❌ self.clear_tp_state(sym)  # Redis에서 상태 관리
                                    await asyncio.sleep(3)
                                    entry_levels_long[sym] = 0
                                    positions = self._shared_status.get("positions", {})
                                    reference_prices[sym] = price
                                    # ✅ cycle_orders는 Redis에서 관리하므로 로컬 정리 제거
                                    # ❌ self.loss_tracker[sym] = 0.0  # Redis 기반으로 대체
                                    # ✅ 레버리지 설정 (최초 1회만) - 중복 제거
                                    # 이미 위에서 설정했으므로 제거
                                    if self.stop_on_profit:
                                        all_flat = True
                                        for coin in self.coin_settings:
                                            sym = coin["symbol"].upper()
                                            try:
                                                positions = self._shared_status.get("positions", {})
                                                amt = float(positions.get(sym, {}).get("positionAmt", 0))
                                                if abs(amt) > 0.0001:
                                                    all_flat = False
                                                    break
                                            except Exception as e:
                                                pass
                                                all_flat = False
                                                break

                                        if all_flat:
                                            self.logger.info(f"[{self.username}] 전략 종료")
                                            self._running = False
                                except Exception as e:
                                    pass

                        else:
                            # ✅ 가격 유효성 검증
                            if entry_price is None or entry_price <= 0:
                                continue
                            tp_price = entry_price * (1 + tp_pct/100) if position_amt > 0 else entry_price * (1 - tp_pct/100)

                            # ✅ 익절 조건 디버깅 (최초 1회만)
                            if not hasattr(self, '_profit_check_logged') or sym not in getattr(self, '_profit_check_logged', set()):
                                if not hasattr(self, '_profit_check_logged'):
                                    self._profit_check_logged = set()
                                self._profit_check_logged.add(sym)

                                current_roi = ((price - entry_price) / entry_price * 100) if position_amt > 0 else ((entry_price - price) / entry_price * 100)
                                self.logger.info(f"[{self.username}] {sym} 익절 체크: 현재가={price:.4f}, 진입가={entry_price:.4f}, 목표가={tp_price:.4f}, 현재수익률={current_roi:.2f}%, 목표수익률={tp_pct:.2f}%")

                            # 익절 조건 만족 시 포지션 종료
                            if (position_amt > 0 and price >= tp_price) or (position_amt < 0 and price <= tp_price):
                                q_close = abs(position_amt)
                                side = "SELL" if position_amt > 0 else "BUY"
                                try:
                                    self.client.futures_create_order(
                                        symbol=sym, side=side, type="MARKET", quantity=format_quantity(q_close, sym))
                                    self.logger.info(f"[{self.username}] {sym} 익절 완료")
                                    await asyncio.sleep(1)
                                    notifier.send_message(f"[익절] {self.username} - {sym} 포지션 종료\n수익률: {tp_pct:.2f}%\n잔고: {self._shared_status['balance']:.2f} USDT")
                                    self.client.futures_cancel_all_open_orders(symbol=sym)
                                    entry_levels_long[sym] = 0
                                    entry_levels_short[sym] = 0
                                    if self.stop_on_profit:
                                        all_flat = True
                                        for coin in self.coin_settings:
                                            sym = coin["symbol"].upper()
                                            try:
                                                positions = self._shared_status.get("positions", {})
                                                amt = float(positions.get(sym, {}).get("positionAmt", 0))
                                                if abs(amt) > 0.0001:
                                                    all_flat = False
                                                    break
                                            except Exception as e:
                                                pass
                                                all_flat = False
                                                break

                                        if all_flat:
                                            self.logger.info(f"[{self.username}] 전략 종료")
                                            self._running = False
                                except Exception as e:
                                    pass
                        stop_loss_pct = current_coin.get("stop_loss_pct",None)
                        
                        if self.strategy_type == "grid" and stop_loss_pct:
                            entry_usdt = abs(entry_price * position_amt)
                            current_usdt = abs(price * position_amt)

                            if entry_usdt == 0:
                                continue  # 나누기 0 방지

                            loss_pct = (entry_usdt - current_usdt) / entry_usdt

                            if loss_pct >= (stop_loss_pct / 100):
                                q_close = adjust_qty(abs(position_amt), step_size)
                                side = "SELL" if position_amt > 0 else "BUY"
                                try:
                                    self.client.futures_create_order(
                                        symbol=sym, side=side, type="MARKET", quantity=format_quantity(q_close, sym))
                                    self.logger.info(f"[{self.username}] {sym} 손절 실행")
                                    notifier.send_message(f"[손절] {self.username} - {sym} 포지션 종료\n손실률: {loss_pct:.2%}\n잔고: {self._shared_status['balance']:.2f} USDT")
                                    self.client.futures_cancel_all_open_orders(symbol=sym)
                                    if position_amt > 0:
                                        entry_levels_long[sym] = 0
                                    else:
                                        entry_levels_short[sym] = 0
                                    reference_prices[sym] = price
                                except Exception as e:
                                    pass

                        if strategy_type == "cycle" and stop_loss_pct:
                            entry_usdt = self.cycle_entry_usdt_map.get(sym)
                            if entry_usdt:
                                unrealized_pnl = (price - entry_price) * position_amt  # 롱 기준
                                if position_amt < 0:
                                    unrealized_pnl = (entry_price - price) * abs(position_amt)

                                # 🆕 Redis에서 실현손익 조회 (손실은 음수)
                                realized_pnl = self.get_position_realized_pnl(sym)
                                total_loss = abs(realized_pnl) if realized_pnl < 0 else 0.0
                                if ((-unrealized_pnl) + total_loss) > entry_usdt * (stop_loss_pct / 100):
                                    q_close = adjust_qty(abs(position_amt), step_size)
                                    try:
                                        self.client.futures_create_order(
                                            symbol=sym, side="SELL", type="MARKET", quantity=format_quantity(q_close, sym)
                                        )
                                        self.logger.info(f"[{self.username}] {sym} cycle 손절 실행")
                                        notifier.send_message(f"[손절] {self.username} - {sym} 포지션 종료\n손실 합계: {-(unrealized_pnl+total_loss):.2f} USDT")
                                        self.client.futures_cancel_all_open_orders(symbol=sym)
                                        # ❌ self.clear_tp_state(sym)  # Redis에서 상태 관리
                                        entry_levels_long[sym] = 0
                                        reference_prices[sym] = price
                                        # ❌ self.loss_tracker[sym] = 0.0  # Redis 기반으로 대체
                                        # ✅ cycle_orders는 Redis에서 관리하므로 로컬 정리 제거
                                        self.cycle_entry_usdt_map.pop(sym, None)  # ❌ 진입 기록 제거
                                    except Exception as e:
                                        pass

                await asyncio.sleep(interval)

        except Exception as e:
            tb = traceback.format_exc()
            self.logger.error(f"[{self.username}] 전략 실행 중 오류:\n{tb}")
        finally:
            self._running = False
            # 🔧 PID 파일 정리 - 전략 종료 시 반드시 실행
            self._cleanup_pid_file()
            self.logger.info(f"[{self.username}] 전략 종료.")

    async def stop(self):
        if self._running:
            self.logger.info(f"[{self.username}] 전략 중지")

            try:
                status = get_user_status(self.username)
                positions = (status.get("positions", {}))
                if isinstance(positions, list):
                    positions = {p.get("symbol"): {"positionAmt": p.get("positionAmt"), "entryPrice": p.get("entryPrice")} for p in positions}

                for symbol, data in positions.items():
                    symbol = symbol.upper()
                    try:
                        self.client.futures_cancel_all_open_orders(symbol=symbol)
                        pass
                    except Exception:
                        pass

                    try:
                        amt = float(data.get("positionAmt", 0))
                        if abs(amt) > 0.0001:
                            side = "SELL" if amt > 0 else "BUY"
                            self.client.futures_create_order(
                                symbol=symbol, side=side, type="MARKET", quantity=format_quantity(abs(amt), symbol)
                            )
                            self.logger.info(f"[{self.username}] {symbol} 포지션 종료")
                    except Exception:
                        pass

            except Exception as e:
                pass

            self._running = False
            self._paused = False
            # 🔧 PID 파일 정리
            self._cleanup_pid_file()
            # 전략 중지 완료 로그 제거

    def _cleanup_pid_file(self):
        """PID 파일 및 메모리 정리"""
        try:
            pid_file = f"pids/{self.username}.pid"
            if os.path.exists(pid_file):
                os.remove(pid_file)
                pass
        except Exception:
            pass

        # ✅ 메모리 누수 방지를 위한 딕셔너리 정리
        try:
            # ✅ cycle_orders는 Redis에서 관리하므로 로컬 정리 제거
            self.cycle_entry_usdt_map.clear()
            self.usdt_per_layer_map.clear()
            self.leverage_set.clear()
            self.logger.info(f"[{self.username}] 메모리 정리 완료")
        except Exception as e:
            self.logger.warning(f"[{self.username}] 메모리 정리 실패: {e}")

    async def execute_cycle_entry(self, symbol, coin_config, current_price, balance, fraction, leverage, filters, precision, reference_prices, entry_levels_long):
        """
        순환매매 진입 로직 실행

        Args:
            symbol: 심볼 (예: BTCUSDT)
            coin_config: 코인 설정
            current_price: 현재 가격
            balance: 잔고
            fraction: 진입 비율
            leverage: 레버리지
            filters: 바이낸스 필터 정보
            precision: 가격 정밀도
            reference_prices: 기준가 딕셔너리
            entry_levels_long: 진입 레벨 딕셔너리
        """
        try:
            # ✅ 이익 후 중지 모드에서는 순환매매 진입 금지
            if self.stop_on_profit:
                return

            # ✅ 중복 진입 방지: 이미 포지션이 있는지 재확인
            try:
                positions = self.client.futures_position_information(symbol=symbol)
                for pos in positions:
                    position_amt = float(pos.get("positionAmt", 0))
                    if abs(position_amt) > 0.0001:
                        logging.info(f"[{symbol}] 중복 진입 방지: 기존 포지션 {position_amt}개 존재")
                        return
            except Exception as e:
                logging.warning(f"[{symbol}] 포지션 확인 실패: {e}")
                # 포지션 확인 실패 시에도 진입 중단 (안전장치)
                return
            # 1. 설정값 로드
            layer_count = coin_config.get("cycle_layer_count", 10)
            bottom_pct = coin_config.get("cycle_bottom_layer_pct", 50.0)

            # ✅ 순환매매 설정값 안전성 검증
            if layer_count is None or layer_count <= 0:
                layer_count = 10  # 기본값
            if bottom_pct is None or bottom_pct <= 0:
                bottom_pct = 50.0  # 기본값

            self.logger.info(f"[{self.username}] {symbol} 순환매매 진입")

            # 2. 가격 범위 계산 (균등 간격)
            # ✅ 가격 유효성 검증
            if current_price is None or current_price <= 0:
                self.logger.warning(f"[{self.username}] {symbol} 순환매매 진입 실패: 현재가 오류 ({current_price})")
                return
            bottom_price = current_price * (1 - bottom_pct / 100)
            price_gap = (current_price - bottom_price) / (layer_count - 1) if layer_count > 1 else 0

            # 3. 주문 상태 초기화 (웹소켓에서 자동 관리)
            pass  # 웹소켓 매니저에서 새 주문 생성 시 자동 초기화

            # 4. 주문량 계산 (모든 레이어 동일한 USDT 값)
            # ✅ 안전한 나눗셈을 위한 검증
            if balance is None or balance <= 0:
                self.logger.warning(f"[{self.username}] {symbol} 순환매매 진입 실패: 잔고 오류 ({balance})")
                return

            # layer_count는 이미 위에서 안전하게 검증됨

            total_usdt = balance * fraction
            usdt_per_layer = total_usdt / layer_count

            # 진입 금액 로그 제거

            # 5. 필터 정보 추출
            step_size = filters["stepSize"]
            min_qty = filters["minQty"]
            min_notional = filters["minNotional"]
            min_price = filters["minPrice"]

            # 6. 레이어별 주문 실행
            successful_orders = 0
            failed_orders = 0

            for i in range(layer_count):
                # 6-1. 레이어 가격 계산 (균등 간격)
                layer_price = round_price(current_price - (price_gap * i), precision)

                # 6-2. 가격 유효성 검사
                if layer_price <= 0:
                    continue

                if layer_price < min_price:
                    continue

                # 6-3. 주문량 계산
                # ✅ 안전한 나눗셈을 위한 검증
                if layer_price is None or layer_price <= 0:
                    continue

                order_value = usdt_per_layer * leverage
                raw_qty = order_value / layer_price
                adjusted_qty = adjust_qty(raw_qty, step_size)

                # 디버깅: 주문량 계산 과정 로그
                logging.info(f"[{symbol}] 레이어{i}: 가격={layer_price}, 원본수량={raw_qty:.8f}, 조정수량={adjusted_qty:.8f}, stepSize={step_size}")

                # 최소 수량 보정
                if adjusted_qty < min_qty and raw_qty >= min_qty:
                    adjusted_qty = min_qty
                    logging.info(f"[{symbol}] 레이어{i}: 최소수량 보정 {min_qty}")

                # 6-4. 주문량 유효성 검사
                if adjusted_qty <= 0:
                    logging.warning(f"[{symbol}] 레이어{i}: 수량이 0 이하 ({adjusted_qty})")
                    continue

                if layer_price * adjusted_qty < min_notional:
                    logging.warning(f"[{symbol}] 레이어{i}: 최소금액 미달 ({layer_price * adjusted_qty:.2f} < {min_notional})")
                    continue

                # 6-5. 주문 실행
                try:
                    if i == 0:
                        # 첫 번째 레이어: 시장가 매수
                        formatted_qty = format_quantity(adjusted_qty, symbol)

                        # 디버깅: 주문 파라미터 로그
                        logging.info(f"[{symbol}] 레이어{i} 시장가 주문 파라미터: quantity={formatted_qty}")

                        self.client.futures_create_order(
                            symbol=symbol,
                            side="BUY",
                            type="MARKET",
                            quantity=formatted_qty
                        )
                        self.logger.info(f"[{self.username}] {symbol} 시장가 매수: {adjusted_qty}")

                        # 시장가 매수는 웹소켓에서 자동 감지됨
                        pass  # 웹소켓 매니저에서 시장가 체결 시 자동 업데이트

                        # 기준가 설정
                        reference_prices[symbol] = layer_price

                    else:
                        # 나머지 레이어: 지정가 매수
                        formatted_price = format_price(layer_price, symbol)
                        formatted_qty = format_quantity(adjusted_qty, symbol)

                        # 디버깅: 주문 파라미터 로그
                        logging.info(f"[{symbol}] 레이어{i} 지정가 주문 파라미터: price={formatted_price}, quantity={formatted_qty}")

                        self.client.futures_create_order(
                            symbol=symbol,
                            side="BUY",
                            type="LIMIT",
                            quantity=formatted_qty,
                            price=formatted_price,
                            timeInForce="GTC"
                        )
                        self.logger.info(f"[{self.username}] {symbol} 지정가 주문: {layer_price} x {adjusted_qty}")

                        # 지정가 주문은 웹소켓에서 자동 추가됨
                        pass  # 웹소켓 매니저에서 지정가 주문 생성 시 자동 추가

                    successful_orders += 1

                except Exception as e:
                    error_msg = self._parse_binance_error(str(e))
                    tb = traceback.format_exc()
                    self.logger.info(f"[{self.username}] {symbol} 주문 실패: {error_msg}")
                    self.logger.debug(f"[{self.username}] {symbol} 레이어{i} 주문 실패 상세:\n{tb}")
                    await self.send_error_report(symbol, f"레이어{i} 주문 실패: {error_msg}", tb)

                    # 잔고 부족 시 더 이상 주문 시도하지 않음
                    if "margin is insufficient" in str(e).lower() or "insufficient" in str(e).lower():
                        logging.info(f"[{symbol}] 잔고 부족으로 인한 주문 중단")
                        break

                    failed_orders += 1
                    continue

            # 7. 진입 완료 처리
            if successful_orders > 0:
                entry_levels_long[symbol] = successful_orders
                self.usdt_per_layer_map[symbol] = usdt_per_layer
                self.cycle_entry_usdt_map[symbol] = total_usdt

                self.logger.info(f"[{self.username}] {symbol} 순환매매 진입 완료: {successful_orders}개 레이어")
                await self.send_success_report(symbol, f"순환매매 진입 완료: {successful_orders}/{layer_count}개 레이어 성공")
            else:
                self.logger.info(f"[{self.username}] {symbol} 순환매매 진입 실패")
                await self.send_error_report(symbol, "순환매매 진입 실패: 모든 주문 실패")

        except Exception as e:
            tb = traceback.format_exc()
            error_msg = f"순환매매 진입 중 오류: {str(e)}"
            self.logger.info(f"[{self.username}] {symbol} {error_msg}")
            self.logger.debug(f"[{self.username}] {symbol} 순환매매 진입 오류 상세:\n{tb}")
            await self.send_error_report(symbol, error_msg, tb)

# Registry and signal handling
trading_managers = {}
def get_trading_manager(user, key, sec, coin_settings=None, strategy_type=None):
    manager = trading_managers.get(user)
    if not manager:
        manager = TradingManager(user, key, sec)
        trading_managers[user] = manager
    if coin_settings is not None:
        manager.coin_settings = coin_settings
    if strategy_type is not None:
        manager.strategy_type = strategy_type
    return manager

active_manager = None
def handle_stop_signal(signum, frame):
    """시그널 핸들러 - 사용하지 않는 매개변수 무시"""
    _ = signum, frame  # 사용하지 않는 매개변수 명시적 무시
    global active_manager
    if active_manager:
        active_manager.stop_on_profit = True
        logger = get_user_logger(active_manager.username)
        logger.info(f"[{active_manager.username}] 익절 후 중지 시그널 수신.")
signal.signal(signal.SIGUSR1, handle_stop_signal)

if __name__ == "__main__":
    import sys
    import asyncio
    from run_strategy import get_trading_manager

    if len(sys.argv) < 2:
        print("❌ 사용자명을 인자로 제공해야 합니다.")
        sys.exit(1)

    username = sys.argv[1]

    # 사용자 DB에서 API 키 불러오기
    import sqlite3
    conn = sqlite3.connect("user_settings.db")
    cursor = conn.cursor()
    cursor.execute("SELECT api_key, secret_key FROM users WHERE username = ?", (username,))
    row = cursor.fetchone()
    conn.close()

    if not row:
        print(f"❌ {username} 사용자 정보를 찾을 수 없습니다.")
        sys.exit(1)

    api_key, secret_key = row

    manager = get_trading_manager(username, api_key, secret_key)
    asyncio.run(manager.run())