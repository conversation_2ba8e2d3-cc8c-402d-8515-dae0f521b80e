#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
지표 계산 테스트 스크립트
IOTA 실제 데이터로 Stochastic RSI 계산 검증
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from technical_indicators import calculate_rsi, calculate_stochastic_rsi, analyze_conditional_entry

# IOTA 캔들 데이터 (제공받은 데이터)
iota_candles = [
    {"timestamp": 1751371200000, "open": 0.1538, "high": 0.1556, "low": 0.1524, "close": 0.1528, "volume": 2012914.0},
    {"timestamp": 1751385600000, "open": 0.153, "high": 0.1537, "low": 0.1502, "close": 0.1502, "volume": 1578852.0},
    {"timestamp": 1751400000000, "open": 0.1502, "high": 0.1522, "low": 0.1496, "close": 0.1513, "volume": 1440483.0},
    {"timestamp": 1751414400000, "open": 0.1513, "high": 0.1514, "low": 0.1484, "close": 0.1506, "volume": 1397979.0},
    {"timestamp": 1751428800000, "open": 0.1506, "high": 0.155, "low": 0.1506, "close": 0.1546, "volume": 2183277.0},
    {"timestamp": 1751443200000, "open": 0.1547, "high": 0.157, "low": 0.1545, "close": 0.1555, "volume": 1434567.0},
    {"timestamp": 1751457600000, "open": 0.1555, "high": 0.159, "low": 0.154, "close": 0.1589, "volume": 3094720.0},
    {"timestamp": 1751472000000, "open": 0.1589, "high": 0.1659, "low": 0.1589, "close": 0.1655, "volume": 6427651.0},
    {"timestamp": 1751486400000, "open": 0.1655, "high": 0.1672, "low": 0.1634, "close": 0.1636, "volume": 2583624.0},
    {"timestamp": 1751500800000, "open": 0.1636, "high": 0.1657, "low": 0.1631, "close": 0.1638, "volume": 2316383.0},
    {"timestamp": 1751515200000, "open": 0.1638, "high": 0.1685, "low": 0.1636, "close": 0.1679, "volume": 3026363.0},
    {"timestamp": 1751529600000, "open": 0.168, "high": 0.1682, "low": 0.1647, "close": 0.1651, "volume": 2389303.0},
    {"timestamp": 1751544000000, "open": 0.165, "high": 0.1672, "low": 0.1612, "close": 0.1616, "volume": 4253924.0},
    {"timestamp": 1751558400000, "open": 0.1617, "high": 0.1635, "low": 0.1617, "close": 0.1634, "volume": 1694536.0},
    {"timestamp": 1751572800000, "open": 0.1634, "high": 0.1649, "low": 0.1632, "close": 0.1639, "volume": 1192941.0},
    {"timestamp": 1751587200000, "open": 0.1639, "high": 0.1648, "low": 0.1611, "close": 0.1614, "volume": 1359756.0},
    {"timestamp": 1751601600000, "open": 0.1613, "high": 0.1621, "low": 0.1574, "close": 0.1575, "volume": 2049489.0},
    {"timestamp": 1751616000000, "open": 0.1574, "high": 0.1591, "low": 0.1565, "close": 0.1584, "volume": 1055169.0},
    {"timestamp": 1751630400000, "open": 0.1584, "high": 0.1589, "low": 0.1567, "close": 0.1568, "volume": 1041361.0}
]

def test_indicators():
    """지표 계산 테스트"""
    print("=" * 80)
    print("🔍 IOTA 지표 계산 테스트 (수정된 로직)")
    print("=" * 80)
    
    # 종가 데이터 추출
    closes = [candle["close"] for candle in iota_candles]
    
    print(f"\n📊 캔들 데이터 정보:")
    print(f"   - 캔들 개수: {len(closes)}")
    print(f"   - 첫 번째 종가: {closes[0]:.4f}")
    print(f"   - 마지막 종가: {closes[-1]:.4f}")
    print(f"   - 가격 범위: {min(closes):.4f} ~ {max(closes):.4f}")
    
    # 1. RSI 계산 (수정된 EMA 방식)
    print(f"\n📈 RSI 계산 (EMA 방식):")
    rsi_values = calculate_rsi(closes, period=14)
    
    # 최근 5개 RSI 값 출력
    print(f"   최근 5개 RSI 값:")
    for i in range(max(0, len(rsi_values)-5), len(rsi_values)):
        print(f"     [{i:2d}] 종가: {closes[i]:.4f} → RSI: {rsi_values[i]:.2f}")
    
    # 2. Stochastic RSI 계산
    print(f"\n📊 Stochastic RSI 계산:")
    rsi_vals, stoch_k_vals, stoch_d_vals = calculate_stochastic_rsi(closes, 
                                                                   rsi_period=14, 
                                                                   stoch_period=14, 
                                                                   k_period=3, 
                                                                   d_period=3)
    
    # 최근 5개 Stochastic RSI 값 출력
    print(f"   최근 5개 Stochastic RSI 값:")
    for i in range(max(0, len(stoch_k_vals)-5), len(stoch_k_vals)):
        print(f"     [{i:2d}] RSI: {rsi_vals[i]:.2f} → K: {stoch_k_vals[i]:.2f}, D: {stoch_d_vals[i]:.2f}")
    
    # 3. 조건부 진입 분석
    print(f"\n🎯 조건부 진입 분석:")
    print(f"   캔들 개수: {len(iota_candles)} (최소 요구량: 20)")

    analysis = analyze_conditional_entry(iota_candles)

    print(f"   현재가: {analysis['current_price']:.6f}")
    print(f"   RSI(14): {analysis['rsi']:.2f}")
    print(f"   Stoch RSI K: {analysis['stoch_rsi_k']:.2f}")
    print(f"   Stoch RSI D: {analysis['stoch_rsi_d']:.2f}")
    print(f"   최종 신호: {analysis['signal']}")

    # 직접 계산한 값과 비교
    print(f"\n🔍 직접 계산 vs analyze_conditional_entry 비교:")
    print(f"   직접 계산 - RSI: {rsi_vals[-1]:.2f}, K: {stoch_k_vals[-1]:.2f}, D: {stoch_d_vals[-1]:.2f}")
    print(f"   함수 결과  - RSI: {analysis['rsi']:.2f}, K: {analysis['stoch_rsi_k']:.2f}, D: {analysis['stoch_rsi_d']:.2f}")
    print(f"   일치 여부: {'✅' if abs(rsi_vals[-1] - analysis['rsi']) < 0.01 else '❌'}")
    
    # 4. 조건 확인
    print(f"\n✅ 조건 확인:")
    k = analysis['stoch_rsi_k']
    d = analysis['stoch_rsi_d']
    rsi = analysis['rsi']
    
    print(f"   K > D: {k > d} ({k:.2f} {'>' if k > d else '<'} {d:.2f})")
    print(f"   RSI > 20: {rsi > 20} ({rsi:.2f} {'>' if rsi > 20 else '<='} 20)")
    print(f"   RSI < 80: {rsi < 80} ({rsi:.2f} {'<' if rsi < 80 else '>='} 80)")
    
    # 5. 신호 로직 확인
    print(f"\n🔍 신호 생성 로직:")
    if k > d and rsi > 20:
        print(f"   → 롱 조건 충족: K > D AND RSI > 20")
        expected_signal = "long"
    elif k < d and rsi < 80:
        print(f"   → 숏 조건 충족: K < D AND RSI < 80")
        expected_signal = "short"
    else:
        print(f"   → 대기: 조건 불충족")
        expected_signal = "wait"
    
    print(f"   예상 신호: {expected_signal}")
    print(f"   실제 신호: {analysis['signal']}")
    print(f"   일치 여부: {'✅' if expected_signal == analysis['signal'] else '❌'}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_indicators()
