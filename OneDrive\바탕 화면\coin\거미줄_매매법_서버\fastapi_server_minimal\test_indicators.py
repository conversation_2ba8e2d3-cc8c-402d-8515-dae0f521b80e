#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
지표 계산 테스트 스크립트
IOTA 실제 데이터로 Stochastic RSI 계산 검증
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from technical_indicators import calculate_rsi, calculate_stochastic_rsi, analyze_conditional_entry

# IOTA 캔들 데이터 (확장된 데이터 - 52개)
iota_candles = [
    {"timestamp": 1750896000000, "open": 0.1566, "high": 0.1607, "low": 0.1563, "close": 0.1583, "volume": 2241993.0},
    {"timestamp": 1750910400000, "open": 0.1583, "high": 0.1586, "low": 0.1561, "close": 0.1579, "volume": 1463556.0},
    {"timestamp": 1750924800000, "open": 0.1578, "high": 0.1588, "low": 0.1556, "close": 0.1563, "volume": 2088640.0},
    {"timestamp": 1750939200000, "open": 0.1562, "high": 0.1563, "low": 0.1516, "close": 0.1531, "volume": 4549982.0},
    {"timestamp": 1750953600000, "open": 0.153, "high": 0.1532, "low": 0.1508, "close": 0.1526, "volume": 3741119.0},
    {"timestamp": 1750968000000, "open": 0.1526, "high": 0.1551, "low": 0.152, "close": 0.1524, "volume": 2538306.0},
    {"timestamp": 1750982400000, "open": 0.1525, "high": 0.1541, "low": 0.1506, "close": 0.1534, "volume": 2164800.0},
    {"timestamp": 1750996800000, "open": 0.1533, "high": 0.1544, "low": 0.1515, "close": 0.1519, "volume": 1759862.0},
    {"timestamp": 1751011200000, "open": 0.152, "high": 0.1548, "low": 0.1519, "close": 0.1532, "volume": 2270005.0},
    {"timestamp": 1751025600000, "open": 0.1532, "high": 0.1544, "low": 0.1515, "close": 0.1541, "volume": 3808344.0},
    {"timestamp": 1751040000000, "open": 0.1541, "high": 0.1561, "low": 0.1504, "close": 0.1518, "volume": 2067779.0},
    {"timestamp": 1751054400000, "open": 0.1518, "high": 0.1546, "low": 0.1517, "close": 0.1544, "volume": 2706023.0},
    {"timestamp": 1751068800000, "open": 0.1544, "high": 0.1561, "low": 0.1536, "close": 0.1553, "volume": 1534749.0},
    {"timestamp": 1751083200000, "open": 0.1552, "high": 0.1559, "low": 0.1547, "close": 0.1555, "volume": 792762.0},
    {"timestamp": 1751097600000, "open": 0.1555, "high": 0.1556, "low": 0.1536, "close": 0.1539, "volume": 767960.0},
    {"timestamp": 1751112000000, "open": 0.1539, "high": 0.1556, "low": 0.1531, "close": 0.1554, "volume": 1107570.0},
    {"timestamp": 1751126400000, "open": 0.1555, "high": 0.1585, "low": 0.1541, "close": 0.1573, "volume": 3175030.0},
    {"timestamp": 1751140800000, "open": 0.1574, "high": 0.1595, "low": 0.1569, "close": 0.1586, "volume": 614613.0},
    {"timestamp": 1751155200000, "open": 0.1587, "high": 0.1587, "low": 0.1568, "close": 0.1568, "volume": 932154.0},
    {"timestamp": 1751169600000, "open": 0.1569, "high": 0.158, "low": 0.1563, "close": 0.1578, "volume": 1050860.0},
    {"timestamp": 1751184000000, "open": 0.1578, "high": 0.1598, "low": 0.1577, "close": 0.1588, "volume": 1147157.0},
    {"timestamp": 1751198400000, "open": 0.1588, "high": 0.1589, "low": 0.156, "close": 0.1565, "volume": 1997715.0},
    {"timestamp": 1751212800000, "open": 0.1566, "high": 0.157, "low": 0.1559, "close": 0.1568, "volume": 1109241.0},
    {"timestamp": 1751227200000, "open": 0.157, "high": 0.1642, "low": 0.1564, "close": 0.1637, "volume": 2475671.0},
    {"timestamp": 1751241600000, "open": 0.1637, "high": 0.1647, "low": 0.1606, "close": 0.1607, "volume": 1569246.0},
    {"timestamp": 1751256000000, "open": 0.1607, "high": 0.162, "low": 0.1582, "close": 0.162, "volume": 2083044.0},
    {"timestamp": 1751270400000, "open": 0.1621, "high": 0.1621, "low": 0.1587, "close": 0.1597, "volume": 2023375.0},
    {"timestamp": 1751284800000, "open": 0.1596, "high": 0.1604, "low": 0.1566, "close": 0.1591, "volume": 2448787.0},
    {"timestamp": 1751299200000, "open": 0.1592, "high": 0.1632, "low": 0.1582, "close": 0.1629, "volume": 2559084.0},
    {"timestamp": 1751313600000, "open": 0.1628, "high": 0.1633, "low": 0.1591, "close": 0.1593, "volume": 1942681.0},
    {"timestamp": 1751328000000, "open": 0.1593, "high": 0.1599, "low": 0.1579, "close": 0.1584, "volume": 1265330.0},
    {"timestamp": 1751342400000, "open": 0.1583, "high": 0.1584, "low": 0.1543, "close": 0.1565, "volume": 1520647.0},
    {"timestamp": 1751356800000, "open": 0.1565, "high": 0.1565, "low": 0.1535, "close": 0.1538, "volume": 1290067.0},
    {"timestamp": 1751371200000, "open": 0.1538, "high": 0.1556, "low": 0.1524, "close": 0.1528, "volume": 2012914.0},
    {"timestamp": 1751385600000, "open": 0.153, "high": 0.1537, "low": 0.1502, "close": 0.1502, "volume": 1578852.0},
    {"timestamp": 1751400000000, "open": 0.1502, "high": 0.1522, "low": 0.1496, "close": 0.1513, "volume": 1440483.0},
    {"timestamp": 1751414400000, "open": 0.1513, "high": 0.1514, "low": 0.1484, "close": 0.1506, "volume": 1397979.0},
    {"timestamp": 1751428800000, "open": 0.1506, "high": 0.155, "low": 0.1506, "close": 0.1546, "volume": 2183277.0},
    {"timestamp": 1751443200000, "open": 0.1547, "high": 0.157, "low": 0.1545, "close": 0.1555, "volume": 1434567.0},
    {"timestamp": 1751457600000, "open": 0.1555, "high": 0.159, "low": 0.154, "close": 0.1589, "volume": 3094720.0},
    {"timestamp": 1751472000000, "open": 0.1589, "high": 0.1659, "low": 0.1589, "close": 0.1655, "volume": 6427651.0},
    {"timestamp": 1751486400000, "open": 0.1655, "high": 0.1672, "low": 0.1634, "close": 0.1636, "volume": 2583624.0},
    {"timestamp": 1751500800000, "open": 0.1636, "high": 0.1657, "low": 0.1631, "close": 0.1638, "volume": 2316383.0},
    {"timestamp": 1751515200000, "open": 0.1638, "high": 0.1685, "low": 0.1636, "close": 0.1679, "volume": 3026363.0},
    {"timestamp": 1751529600000, "open": 0.168, "high": 0.1682, "low": 0.1647, "close": 0.1651, "volume": 2389303.0},
    {"timestamp": 1751544000000, "open": 0.165, "high": 0.1672, "low": 0.1612, "close": 0.1616, "volume": 4253924.0},
    {"timestamp": 1751558400000, "open": 0.1617, "high": 0.1635, "low": 0.1617, "close": 0.1634, "volume": 1694536.0},
    {"timestamp": 1751572800000, "open": 0.1634, "high": 0.1649, "low": 0.1632, "close": 0.1639, "volume": 1192941.0},
    {"timestamp": 1751587200000, "open": 0.1639, "high": 0.1648, "low": 0.1611, "close": 0.1614, "volume": 1359756.0},
    {"timestamp": 1751601600000, "open": 0.1613, "high": 0.1621, "low": 0.1574, "close": 0.1575, "volume": 2049489.0},
    {"timestamp": 1751616000000, "open": 0.1574, "high": 0.1591, "low": 0.1565, "close": 0.1584, "volume": 1055169.0},
    {"timestamp": 1751630400000, "open": 0.1584, "high": 0.1589, "low": 0.1567, "close": 0.1568, "volume": 1041361.0}
]

def test_indicators():
    """지표 계산 테스트"""
    print("=" * 80)
    print("🔍 IOTA 지표 계산 테스트 (수정된 로직)")
    print("=" * 80)
    
    # 종가 데이터 추출
    closes = [candle["close"] for candle in iota_candles]
    
    print(f"\n📊 캔들 데이터 정보:")
    print(f"   - 캔들 개수: {len(closes)}")
    print(f"   - 첫 번째 종가: {closes[0]:.4f}")
    print(f"   - 마지막 종가: {closes[-1]:.4f}")
    print(f"   - 가격 범위: {min(closes):.4f} ~ {max(closes):.4f}")
    
    # 1. RSI 계산 (수정된 EMA 방식)
    print(f"\n📈 RSI 계산 (EMA 방식):")
    rsi_values = calculate_rsi(closes, period=14)
    
    # 최근 5개 RSI 값 출력
    print(f"   최근 5개 RSI 값:")
    for i in range(max(0, len(rsi_values)-5), len(rsi_values)):
        print(f"     [{i:2d}] 종가: {closes[i]:.4f} → RSI: {rsi_values[i]:.2f}")
    
    # 2. Stochastic RSI 계산
    print(f"\n📊 Stochastic RSI 계산:")
    rsi_vals, stoch_k_vals, stoch_d_vals = calculate_stochastic_rsi(closes, 
                                                                   rsi_period=14, 
                                                                   stoch_period=14, 
                                                                   k_period=3, 
                                                                   d_period=3)
    
    # 최근 5개 Stochastic RSI 값 출력
    print(f"   최근 5개 Stochastic RSI 값:")
    for i in range(max(0, len(stoch_k_vals)-5), len(stoch_k_vals)):
        print(f"     [{i:2d}] RSI: {rsi_vals[i]:.2f} → K: {stoch_k_vals[i]:.2f}, D: {stoch_d_vals[i]:.2f}")
    
    # 3. 조건부 진입 분석
    print(f"\n🎯 조건부 진입 분석:")
    print(f"   캔들 개수: {len(iota_candles)} (최소 요구량: 20)")

    analysis = analyze_conditional_entry(iota_candles)

    print(f"   현재가: {analysis['current_price']:.6f}")
    print(f"   RSI(14): {analysis['rsi']:.2f}")
    print(f"   Stoch RSI K: {analysis['stoch_rsi_k']:.2f}")
    print(f"   Stoch RSI D: {analysis['stoch_rsi_d']:.2f}")
    print(f"   최종 신호: {analysis['signal']}")

    # 직접 계산한 값과 비교
    print(f"\n🔍 직접 계산 vs analyze_conditional_entry 비교:")
    print(f"   직접 계산 - RSI: {rsi_vals[-1]:.2f}, K: {stoch_k_vals[-1]:.2f}, D: {stoch_d_vals[-1]:.2f}")
    print(f"   함수 결과  - RSI: {analysis['rsi']:.2f}, K: {analysis['stoch_rsi_k']:.2f}, D: {analysis['stoch_rsi_d']:.2f}")
    print(f"   일치 여부: {'✅' if abs(rsi_vals[-1] - analysis['rsi']) < 0.01 else '❌'}")
    
    # 4. 조건 확인
    print(f"\n✅ 조건 확인:")
    k = analysis['stoch_rsi_k']
    d = analysis['stoch_rsi_d']
    rsi = analysis['rsi']
    
    print(f"   K > D: {k > d} ({k:.2f} {'>' if k > d else '<'} {d:.2f})")
    print(f"   RSI > 20: {rsi > 20} ({rsi:.2f} {'>' if rsi > 20 else '<='} 20)")
    print(f"   RSI < 80: {rsi < 80} ({rsi:.2f} {'<' if rsi < 80 else '>='} 80)")
    
    # 5. 신호 로직 확인
    print(f"\n🔍 신호 생성 로직:")
    if k > d and rsi > 20:
        print(f"   → 롱 조건 충족: K > D AND RSI > 20")
        expected_signal = "long"
    elif k < d and rsi < 80:
        print(f"   → 숏 조건 충족: K < D AND RSI < 80")
        expected_signal = "short"
    else:
        print(f"   → 대기: 조건 불충족")
        expected_signal = "wait"
    
    print(f"   예상 신호: {expected_signal}")
    print(f"   실제 신호: {analysis['signal']}")
    print(f"   일치 여부: {'✅' if expected_signal == analysis['signal'] else '❌'}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_indicators()
